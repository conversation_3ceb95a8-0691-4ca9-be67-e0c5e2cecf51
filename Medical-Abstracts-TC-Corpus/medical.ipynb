TRAIN_SIZE = 25
!python Baseline/select_medical.py \
  --preprocessed_data_dir="preprocessed_data/" \
  --output_file="Baseline/selected_train_ids.json" \
  --train_size={TRAIN_SIZE} \
  --random_seed=42 \
  --n_folds=10 \
  --outer_split_size=0.8

!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Baseline/selected_train_ids.json --metrics_output_file Baseline/results_selected_subset.json



import os
import json
import numpy as np
import matplotlib.pyplot as plt

train_ids_path = os.path.join('preprocessed_data', 'train_sample_ids.npy')
train_sample_ids = np.load(train_ids_path)
max_train_size = len(train_sample_ids)

max_train_size

fixed_small_sizes = [25, 60, 100, 200, 500, 1000]



results = []
for train_size in fixed_small_sizes:
    print(f'Running for train_size={train_size}')
    # Run selection
    !python select_medical.py \
      --preprocessed_data_dir="preprocessed_data/" \
      --output_file="selected_train_ids.json" \
      --train_size={train_size} \
      --random_seed=42 \
      --n_folds=10 \
      --outer_split_size=0.8

    # Run evaluation
    !python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json selected_train_ids.json --metrics_output_file results_selected_subset.json

    with open('results_selected_subset.json', 'r') as f:
        metrics = json.load(f)
    metrics['train_size'] = train_size
    results.append(metrics)

with open('train_size_vs_f1.json', 'w') as f:
    json.dump(results, f, indent=2)

print('Sweep complete. Results saved to train_size_vs_f1.json')

import numpy as np
import json

all_ids = np.load('preprocessed_data/train_sample_ids.npy')


with open('selected_train_ids.json', 'w') as f:
    json.dump({"selected_ids": all_ids.tolist()}, f)
print(f"Saved all {len(all_ids)} training IDs to selected_train_ids.json")

!python eval_medical.py --use_full_training=True

import numpy as np
import matplotlib.pyplot as plt

train_sizes = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores = [0.0999, 0.1000, 0.1792, 0.3471, 0.4952, 0.5189, 0.5087]
std_devs = [0.0000, 0.0002, 0.0467, 0.0155, 0.0099, 0.0075, 0.0000]

plt.figure(figsize=(10, 6))
plt.errorbar(train_sizes, f1_scores, yerr=std_devs, fmt='o-', capsize=5)

plt.xscale('log')

plt.xlabel('Training Set Size (log scale)')
plt.ylabel('F1 Macro Score')
plt.title('F1 Macro Score vs Training Set Size')

plt.grid(True, which="both", ls="-", alpha=0.2)

plt.show()

import json
import matplotlib.pyplot as plt

with open('train_size_vs_f1.json', 'r') as f:
    results = json.load(f)

train_sizes = [r['train_size'] for r in results]
f1_scores = [r['average_f1_macro'] for r in results]

plt.figure(figsize=(8,5))
plt.plot(train_sizes, f1_scores, marker='o')
plt.xscale('log')
plt.xlabel('Training Set Size (log scale)')
plt.ylabel('Average F1 Macro')
plt.title('F1 Macro vs. Training Set Size')
plt.grid(True, which='both', ls='--', alpha=0.5)
plt.tight_layout()
plt.show()

TRAIN_SIZE = 25 
!python Cleanlab/select_cleanlab_medical.py \
  --preprocessed_data_dir="preprocessed_data/" \
  --output_file="Cleanlab/selected_train_ids_cleanlab.json" \
  --train_size={TRAIN_SIZE} \
  --random_seed=42 \
  --n_folds=3 \
  --class_balance="equal" \
  --extra_frac=0.5

!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Cleanlab/selected_train_ids_cleanlab.json --metrics_output_file Cleanlab/results_selected_subset.json

pip install "numpy<2"

import json
import numpy as np

fixed_small_sizes = [25, 60, 100, 200, 500, 1000]


log_file = f'cleanlab_sweep_results.txt'

results = []
with open(log_file, 'w') as f:
    f.write("=" * 80 + "\n\n")
    
    for train_size in fixed_small_sizes:
        f.write("=" * 80 + "\n")
        
        # Run cleanlab selection
        !python Cleanlab/select_cleanlab_medical.py \
          --preprocessed_data_dir="preprocessed_data/" \
          --output_file="Cleanlab/selected_train_ids_cleanlab.json" \
          --train_size={train_size} \
          --random_seed=42 \
          --n_folds=3 \
          --class_balance="equal" \
          --extra_frac=0.5
    
        with open('Cleanlab/selected_train_ids_cleanlab.json', 'r') as rf:
            selected_data = json.load(rf)
            selected_ids = np.array(selected_data['selected_ids'])
            

        labels = np.load('preprocessed_data/y_train_full.npy')[selected_ids]
        unique, counts = np.unique(labels, return_counts=True)
        
        f.write(f"\nSelected training set created with {train_size} samples.\n")
        for label, count in zip(unique, counts):
            f.write(f"  Label {label}: {count} samples\n")
        
   
        !python eval_medical.py \
          --preprocessed_data_dir preprocessed_data/ \
          --selected_ids_json Cleanlab/selected_train_ids_cleanlab.json \
          --metrics_output_file results_selected_subset.json
        

        with open('results_selected_subset.json', 'r') as rf:
            metrics = json.load(rf)
        
        f.write("--- Evaluation Results ---\n")
        f.write(f"Individual F1 Macro Scores: {metrics.get('f1_macro_scores', metrics.get('individual_f1_scores', []))}\n")
        f.write(f"Average F1 Macro Score: {metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0)):.4f}\n")
        f.write(f"Standard Deviation of F1 Macro Scores: {metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0)):.4f}\n")
        
      
        f.write("\n\n")
        f.flush()  # Force write to disk

print(f'\nSweep complete! Detailed log saved to {log_file}')

import numpy as np
import matplotlib.pyplot as plt


train_sizes_orig = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_orig = [0.0999, 0.1000, 0.1792, 0.3471, 0.4952, 0.5189, 0.5087]
std_devs_orig = [0.0000, 0.0002, 0.0467, 0.0155, 0.0099, 0.0075, 0.0000]

# Cleanlab data (including full dataset)
train_sizes_cleanlab = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_cleanlab = [0.1569, 0.3716, 0.5255, 0.5683, 0.5887, 0.6046, 0.5087]
std_devs_cleanlab = [0.0924, 0.1302, 0.0210, 0.0046, 0.0010, 0.0011, 0.0000]

plt.figure(figsize=(12, 8))


plt.errorbar(train_sizes_orig, f1_scores_orig, yerr=std_devs_orig, 
             fmt='o-', capsize=5, label='Random Selection', color='blue')

# Plot cleanlab data
plt.errorbar(train_sizes_cleanlab, f1_scores_cleanlab, yerr=std_devs_cleanlab, 
             fmt='s-', capsize=5, label='Cleanlab Selection', color='red')

plt.xscale('log')
plt.grid(True, which="both", ls="-", alpha=0.2)

plt.xlabel('Training Set Size (log scale)')
plt.ylabel('F1 Macro Score')
plt.title('F1 Macro Score vs Training Size: Random vs Cleanlab Selection')
plt.legend()


for x, y in zip(train_sizes_orig, f1_scores_orig):
    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                xytext=(0,10), ha='center', color='blue')
for x, y in zip(train_sizes_cleanlab, f1_scores_cleanlab):
    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                xytext=(0,-15), ha='center', color='red')

plt.tight_layout()
plt.show()

# Add this as a new cell in your medical.ipynb notebook
# DIAGNOSTIC VERSION - Let's see what's actually in the files

import numpy as np
import pandas as pd
import scipy.sparse
import os

print("="*80)
print("DIAGNOSTIC: Checking data files")
print("="*80)

# Check what files exist
print("Files in preprocessed_data/:")
for file in os.listdir("preprocessed_data/"):
    if file.endswith(('.npy', '.npz')):
        filepath = f"preprocessed_data/{file}"
        size = os.path.getsize(filepath)
        print(f"  {file}: {size:,} bytes")

print("\n" + "="*60)
print("LOADING AND INSPECTING DATA")
print("="*60)

# Load y_train_full first (this should work)
print("Loading y_train_full...")
y_train_full = np.load("preprocessed_data/y_train_full.npy")
print(f"y_train_full shape: {y_train_full.shape}")
print(f"y_train_full type: {type(y_train_full)}")
print(f"Classes: {np.unique(y_train_full)}")

# Load train_sample_ids
print("\nLoading train_sample_ids...")
train_sample_ids = np.load("preprocessed_data/train_sample_ids.npy")
print(f"train_sample_ids shape: {train_sample_ids.shape}")
print(f"train_sample_ids type: {type(train_sample_ids)}")

# Now let's carefully inspect X_train_full_vec
print("\nInspecting X_train_full_vec.npz...")
try:
    # Try loading as npz archive first
    data = np.load("preprocessed_data/X_train_full_vec.npz")
    print(f"NPZ archive keys: {list(data.files)}")
    
    for key in data.files:
        arr = data[key]
        print(f"  {key}: shape={arr.shape}, dtype={arr.dtype}, type={type(arr)}")
        
    # Get the main array (usually the first one)
    main_key = data.files[0]
    X_train_full_vec = data[main_key]
    data.close()
    
except Exception as e:
    print(f"Error loading as npz: {e}")
    try:
        # Try loading as sparse matrix
        X_train_full_vec = scipy.sparse.load_npz("preprocessed_data/X_train_full_vec.npz")
        print(f"Loaded as sparse matrix: {type(X_train_full_vec)}")
    except Exception as e2:
        print(f"Error loading as sparse: {e2}")
        X_train_full_vec = None

if X_train_full_vec is not None:
    print(f"\nFinal X_train_full_vec:")
    print(f"  Type: {type(X_train_full_vec)}")
    print(f"  Shape: {X_train_full_vec.shape}")
    if hasattr(X_train_full_vec, 'dtype'):
        print(f"  Dtype: {X_train_full_vec.dtype}")
    
    # Check if shapes match
    print(f"\nShape consistency check:")
    print(f"  X_train_full_vec samples: {X_train_full_vec.shape[0]}")
    print(f"  y_train_full samples: {y_train_full.shape[0]}")
    print(f"  train_sample_ids samples: {train_sample_ids.shape[0]}")
    
    shapes_match = (X_train_full_vec.shape[0] == y_train_full.shape[0] == train_sample_ids.shape[0])
    print(f"  All shapes match: {shapes_match}")
    
    if shapes_match:
        print("\n✓ Data looks good! Proceeding with analysis...")
        
        # Load abstracts
        try:
            with open('preprocessed_data/train_abstracts.txt', 'r', encoding='utf-8') as f:
                abstracts = [line.strip() for line in f.readlines()]
            print(f"✓ Loaded {len(abstracts)} abstracts")
            abstracts_match = len(abstracts) == X_train_full_vec.shape[0]
            print(f"  Abstracts count matches: {abstracts_match}")
        except Exception as e:
            print(f"✗ Could not load abstracts: {e}")
            abstracts = None
        
        print("\n" + "="*60)
        print("QUICK DUPLICATE CHECK")
        print("="*60)
        
        # Quick check for duplicates in a small sample
        sample_size = min(100, X_train_full_vec.shape[0])
        print(f"Checking first {sample_size} samples for duplicates...")
        
        feature_vector_map = {}
        duplicate_count = 0
        
        for idx in range(sample_size):
            if scipy.sparse.issparse(X_train_full_vec):
                feature_vector = X_train_full_vec[idx].toarray().tobytes()
            else:
                feature_vector = X_train_full_vec[idx].tobytes()
            
            label = y_train_full[idx]
            
            if feature_vector in feature_vector_map:
                prev_idx, prev_label = feature_vector_map[feature_vector]
                if prev_label != label:
                    duplicate_count += 1
                    print(f"  Found duplicate: samples {prev_idx} and {idx} have same features but different labels ({prev_label} vs {label})")
                    if abstracts:
                        text1 = abstracts[prev_idx]
                        text2 = abstracts[idx]
                        print(f"    Texts identical: {text1 == text2}")
                        print(f"    Text: {text1[:100]}...")
            else:
                feature_vector_map[feature_vector] = (idx, label)
        
        print(f"\nFound {duplicate_count} duplicate pairs in first {sample_size} samples")
        
        if duplicate_count == 0:
            print("No true duplicates found in the sample.")
            print("This suggests that cleanlab's 'duplicate_issues.txt' contains")
            print("label quality issues, not actual duplicates.")
    else:
        print("\n✗ Shape mismatch! Cannot proceed with analysis.")
else:
    print("\n✗ Could not load X_train_full_vec")

print("\n" + "="*60)
print("CONCLUSION")
print("="*60)
print("Based on this diagnostic:")
print("1. If no true duplicates were found, then cleanlab's 'duplicate_issues.txt'")
print("   contains samples with poor label quality, not actual duplicates")
print("2. Cleanlab identifies potentially mislabeled samples based on model predictions")
print("3. True duplicates would be samples with identical feature vectors but different labels")
print("4. The distinction is important for understanding what cleanlab actually does")

import numpy as np
import pandas as pd
import scipy.sparse
from cleanlab.classification import CleanLearning
import sklearn.linear_model

def load_data_matrix(file_path):
    """Load matrix from .npz file - handles both sparse and dense matrices"""
    try:
        # First try loading as sparse matrix
        return scipy.sparse.load_npz(file_path)
    except ValueError:
        # If that fails, try loading as regular numpy array
        data = np.load(file_path)
        if hasattr(data, 'files'):
            # It's an npz archive, get the first array
            key = data.files[0]
            matrix = data[key]
            data.close()
            return matrix
        else:
            return data

print("="*80)
print("ANALYZING CLEANLAB RESULTS vs TRUE DUPLICATES")
print("="*80)

# Load the data
print("Loading data...")
X_train_full_vec = load_data_matrix("preprocessed_data/X_train_full_vec.npz")
y_train_full = np.load("preprocessed_data/y_train_full.npy")
train_sample_ids = np.load("preprocessed_data/train_sample_ids.npy")

print(f"Loaded data: {X_train_full_vec.shape[0]} samples, {X_train_full_vec.shape[1]} features")
print(f"Data type: {type(X_train_full_vec)}")
print(f"Classes: {np.unique(y_train_full)}")

# Convert to sparse if it's dense (for memory efficiency)
if not scipy.sparse.issparse(X_train_full_vec):
    print("Converting dense matrix to sparse for memory efficiency...")
    X_train_full_vec = scipy.sparse.csr_matrix(X_train_full_vec)

# Load abstracts
try:
    with open('preprocessed_data/train_abstracts.txt', 'r', encoding='utf-8') as f:
        abstracts = [line.strip() for line in f.readlines()]
    print(f"Loaded {len(abstracts)} abstracts")
except Exception as e:
    print(f"Could not load abstracts: {e}")
    abstracts = None

print("\n" + "="*60)
print("ANALYSIS 1: Finding true duplicates (identical feature vectors)")
print("="*60)

# Find true duplicates using feature vectors
duplicate_pairs = []
feature_vector_map = {}

print("Scanning for duplicate feature vectors...")
for idx in range(min(1000, X_train_full_vec.shape[0])):  # Limit to first 1000 for speed
    if idx % 100 == 0:
        print(f"  Processed {idx} samples...")
    
    if scipy.sparse.issparse(X_train_full_vec):
        feature_vector = X_train_full_vec[idx].toarray().tobytes()
    else:
        feature_vector = X_train_full_vec[idx].tobytes()
    
    label = y_train_full[idx]
    
    if feature_vector in feature_vector_map:
        prev_idx, prev_label = feature_vector_map[feature_vector]
        if prev_label != label:
            duplicate_pairs.append({
                'idx1': prev_idx,
                'id1': train_sample_ids[prev_idx],
                'label1': prev_label,
                'idx2': idx,
                'id2': train_sample_ids[idx],
                'label2': label,
                'text1': abstracts[prev_idx] if abstracts else "Text not available",
                'text2': abstracts[idx] if abstracts else "Text not available"
            })
    else:
        feature_vector_map[feature_vector] = (idx, label)

print(f"Found {len(duplicate_pairs)} true duplicate pairs with different labels")

if duplicate_pairs:
    print("\nTrue duplicate pairs:")
    for i, pair in enumerate(duplicate_pairs[:3]):  # Show first 3
        print(f"\nPair {i+1}:")
        print(f"  Sample 1: ID {pair['id1']}, Label {pair['label1']}")
        print(f"  Sample 2: ID {pair['id2']}, Label {pair['label2']}")
        if abstracts:
            print(f"  Texts identical: {pair['text1'] == pair['text2']}")
            print(f"  Text: {pair['text1'][:200]}...")
else:
    print("No true duplicates found in the first 1000 samples!")

print("\n" + "="*60)
print("ANALYSIS 2: Checking feature vector groups")
print("="*60)

# Find all samples with identical feature vectors (regardless of labels)
feature_vector_groups = {}
print("Building feature vector groups...")
for idx in range(min(1000, X_train_full_vec.shape[0])):  # Limit for speed
    if scipy.sparse.issparse(X_train_full_vec):
        feature_vector = X_train_full_vec[idx].toarray().tobytes()
    else:
        feature_vector = X_train_full_vec[idx].tobytes()
    
    if feature_vector not in feature_vector_groups:
        feature_vector_groups[feature_vector] = []
    feature_vector_groups[feature_vector].append(idx)

# Find groups with multiple samples
multi_sample_groups = {k: v for k, v in feature_vector_groups.items() if len(v) > 1}
print(f"Found {len(multi_sample_groups)} feature vector groups with multiple samples")

if multi_sample_groups and abstracts:
    print("\nAnalyzing first few groups:")
    for i, (fv, indices) in enumerate(list(multi_sample_groups.items())[:3]):  # Show first 3
        print(f"\nGroup {i+1} ({len(indices)} samples):")
        texts = [abstracts[idx] for idx in indices]
        labels = [y_train_full[idx] for idx in indices]
        ids = [train_sample_ids[idx] for idx in indices]
        
        for j, (idx, text, label, sample_id) in enumerate(zip(indices, texts, labels, ids)):
            print(f"  Sample {j+1}: ID {sample_id}, Label {label}")
            print(f"    Text: {text[:100]}...")
        
        # Check if all texts are identical
        all_identical = all(text == texts[0] for text in texts)
        print(f"  All texts identical: {all_identical}")
        
        # Check if labels are different
        unique_labels = set(labels)
        print(f"  Unique labels: {unique_labels}")
        if len(unique_labels) > 1:
            print(f"  *** This group has different labels for same feature vector! ***")

print("\n" + "="*60)
print("ANALYSIS 3: Understanding what cleanlab identifies")
print("="*60)

# Run cleanlab to see what it identifies (on a subset for speed)
subset_size = min(1000, X_train_full_vec.shape[0])
X_subset = X_train_full_vec[:subset_size]
y_subset = y_train_full[:subset_size]

clf = sklearn.linear_model.LogisticRegression(solver='liblinear', random_state=42)
cl = CleanLearning(clf, seed=42, verbose=False, cv_n_folds=3)

# Convert labels to 0-indexed
unique_y = np.unique(y_subset)
label_map = {old: new for new, old in enumerate(unique_y)}
y_zero_indexed = np.array([label_map[y] for y in y_subset])

print(f"Running cleanlab analysis on {subset_size} samples...")
issues = cl.find_label_issues(X_subset, y_zero_indexed)

cleanlab_issues = issues[issues['is_label_issue']]
print(f"Cleanlab identified {len(cleanlab_issues)} samples with label issues")

if len(cleanlab_issues) > 0 and abstracts:
    print(f"\nFirst 3 cleanlab-identified issues:")
    for i, idx in enumerate(cleanlab_issues.index[:3]):
        label = y_subset[idx]
        sample_id = train_sample_ids[idx]
        quality_score = issues.loc[idx, 'label_quality']
        text = abstracts[idx]
        print(f"\nIssue {i+1}:")
        print(f"  Sample ID: {sample_id}, Label: {label}")
        print(f"  Quality Score: {quality_score:.3f}")
        print(f"  Text: {text[:200]}...")

print("\n" + "="*60)
print("SUMMARY")
print("="*60)
print(f"True duplicates (same feature vector, different labels): {len(duplicate_pairs)}")
print(f"Feature vector groups with multiple samples: {len(multi_sample_groups)}")
print(f"Cleanlab-identified label issues: {len(cleanlab_issues)}")

# Check overlap
if duplicate_pairs:
    duplicate_indices = set()
    for pair in duplicate_pairs:
        duplicate_indices.add(pair['idx1'])
        duplicate_indices.add(pair['idx2'])
    
    cleanlab_indices = set(cleanlab_issues.index)
    overlap = duplicate_indices.intersection(cleanlab_indices)
    print(f"Overlap between true duplicates and cleanlab issues: {len(overlap)}")

print("\nCONCLUSION:")
print("- Cleanlab identifies samples that appear mislabeled based on model predictions")
print("- This is different from finding identical feature vectors with different labels")
print("- The current 'duplicate_issues.txt' file contains cleanlab issues, not true duplicates")
print("- True duplicates would have identical text but different labels")

# Save results for further analysis
if duplicate_pairs:
    print(f"\nSaving {len(duplicate_pairs)} true duplicate pairs to 'true_duplicates_analysis.txt'")
    with open('true_duplicates_analysis.txt', 'w') as f:
        f.write("TRUE DUPLICATE PAIRS ANALYSIS\n")
        f.write("="*50 + "\n\n")
        for i, pair in enumerate(duplicate_pairs):
            f.write(f"Pair {i+1}:\n")
            f.write(f"  Sample 1: ID {pair['id1']}, Label {pair['label1']}\n")
            f.write(f"  Sample 2: ID {pair['id2']}, Label {pair['label2']}\n")
            f.write(f"  Texts identical: {pair['text1'] == pair['text2']}\n")
            f.write(f"  Text 1: {pair['text1']}\n")
            f.write(f"  Text 2: {pair['text2']}\n")
            f.write("\n" + "-"*50 + "\n\n")

print("\nAnalysis complete!")
print("Note: Analysis was limited to first 1000 samples for performance.")
print("If you want to analyze the full dataset, remove the 'min(1000, ...)' limits.")


# Step 2: Run cleanlab selection with duplicate detection
TRAIN_SIZE = 25
!python Cleanlab/select_cleanlab_medical.py \
  --preprocessed_data_dir="preprocessed_data/" \
  --output_file="Cleanlab/selected_train_ids_cleanlab.json" \
  --train_size={TRAIN_SIZE} \
  --random_seed=42 \
  --n_folds=3 \
  --class_balance="equal" \
  --extra_frac=0.5

# Step 3: Inspect the duplicate detection results
try:
    with open('Cleanlab/duplicate_report.txt', 'r') as f:
        print(f.read())
except FileNotFoundError:
    print("No duplicate report found. Make sure the cleanlab selection completed successfully.")