# Add this as a new cell in your medical.ipynb notebook
# CLEAN DATASET + K-MEDOIDS SELECTION (without cleanlab)

import pandas as pd
import numpy as np
import json
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn_extra.cluster import KMedoids
import scipy.sparse
from collections import Counter
import time

def find_and_remove_duplicates(df, text_column='abstractText', label_column='condition'):
    """Find and remove samples with identical text but different labels."""
    print("🔍 Finding duplicates (identical text with different labels)...")
    
    # Group by text to find duplicates
    text_groups = df.groupby(text_column)
    
    duplicate_info = []
    indices_to_remove = set()
    
    for text, group in text_groups:
        if len(group) > 1:  # Multiple samples with same text
            unique_labels = group[label_column].unique()
            if len(unique_labels) > 1:  # Different labels for same text
                duplicate_info.append({
                    'text': text[:200] + "..." if len(text) > 200 else text,
                    'labels': list(unique_labels),
                    'sample_ids': list(group.index),
                    'count': len(group)
                })
                # Remove all instances of this duplicate text
                indices_to_remove.update(group.index)
    
    print(f"📊 Found {len(duplicate_info)} duplicate text groups with different labels")
    print(f"🗑️  Removing {len(indices_to_remove)} samples total")
    
    if duplicate_info:
        print("\n📋 Duplicate examples:")
        for i, dup in enumerate(duplicate_info[:3]):
            print(f"  {i+1}. Text: {dup['text']}")
            print(f"     Labels: {dup['labels']}")
            print(f"     Count: {dup['count']}")
    
    # Create cleaned dataframe
    cleaned_df = df.drop(indices_to_remove).reset_index(drop=True)
    
    print(f"\n📈 Dataset size: {len(df)} -> {len(cleaned_df)} (removed {len(df) - len(cleaned_df)} samples)")
    
    return cleaned_df, duplicate_info

def preprocess_clean_data(df, text_column='abstractText', label_column='condition', max_features=10000):
    """Preprocess the cleaned data with TF-IDF vectorization."""
    print(f"🔧 Preprocessing data with TF-IDF (max_features={max_features})...")
    
    texts = df[text_column].values
    labels = df[label_column].values
    sample_ids = df.index.values
    
    # TF-IDF vectorization
    vectorizer = TfidfVectorizer(
        max_features=max_features,
        stop_words='english',
        lowercase=True,
        ngram_range=(1, 2),
        min_df=2,
        max_df=0.95
    )
    
    X = vectorizer.fit_transform(texts)
    
    print(f"✅ Vectorized to {X.shape[0]} samples x {X.shape[1]} features")
    print(f"📊 Matrix sparsity: {(1 - X.nnz / (X.shape[0] * X.shape[1])):.3f}")
    
    return X, labels, sample_ids, vectorizer

def balanced_kmedoids_selection(X, labels, sample_ids, target_size, class_balance='equal', random_seed=42):
    """Select a balanced subset using K-medoids clustering."""
    print(f"🎯 Running balanced K-medoids selection for {target_size} samples...")
    
    unique_classes = np.unique(labels)
    num_classes = len(unique_classes)
    
    print(f"📊 Classes: {unique_classes}")
    print(f"📊 Original class distribution:")
    for cls in unique_classes:
        count = np.sum(labels == cls)
        print(f"  Class {cls}: {count} samples")
    
    # Determine per-class target sizes
    if class_balance == 'equal':
        per_class_size = target_size // num_classes
        class_sizes = {cls: per_class_size for cls in unique_classes}
        # Distribute remaining samples
        remaining = target_size - (per_class_size * num_classes)
        for i in range(remaining):
            cls = unique_classes[i % num_classes]
            class_sizes[cls] += 1
    else:  # proportional
        class_counts = {cls: np.sum(labels == cls) for cls in unique_classes}
        total_samples = len(labels)
        class_sizes = {
            cls: max(1, int(target_size * (count / total_samples)))
            for cls, count in class_counts.items()
        }
    
    print(f"🎯 Target class distribution:")
    for cls in unique_classes:
        print(f"  Class {cls}: {class_sizes[cls]} samples")
    
    # Select samples for each class using K-medoids
    selected_indices = []
    
    for cls in unique_classes:
        target_count = class_sizes[cls]
        if target_count <= 0:
            continue
            
        class_indices = np.where(labels == cls)[0]
        available_count = len(class_indices)
        
        print(f"⚙️  Selecting {target_count} from {available_count} samples of class {cls}...")
        
        if target_count >= available_count:
            selected_indices.extend(class_indices)
        else:
            # Use K-medoids
            X_class = X[class_indices]
            
            if scipy.sparse.issparse(X_class):
                X_class_dense = X_class.toarray()
            else:
                X_class_dense = X_class
            
            kmedoids = KMedoids(n_clusters=target_count, init="k-medoids++", random_state=random_seed)
            kmedoids.fit(X_class_dense)
            
            selected_class_indices = class_indices[kmedoids.medoid_indices_]
            selected_indices.extend(selected_class_indices)
    
    selected_indices = np.array(selected_indices)
    selected_sample_ids = sample_ids[selected_indices]
    
    print(f"✅ Selected {len(selected_indices)} samples total")
    print("📊 Final class distribution:")
    selected_labels = labels[selected_indices]
    for cls in unique_classes:
        count = np.sum(selected_labels == cls)
        print(f"  Class {cls}: {count} samples")
    
    return selected_indices, selected_sample_ids

# =============================================================================
# MAIN EXECUTION
# =============================================================================

print("="*80)
print("🧹 CLEAN DATASET + K-MEDOIDS SELECTION")
print("="*80)

# Configuration
INPUT_FILE = 'medical_train.csv'
OUTPUT_FILE = 'selected_train_ids_clean_kmedoids.json'
TRAIN_SIZE = 1000
MAX_FEATURES = 10000
CLASS_BALANCE = 'equal'  # or 'proportional'
RANDOM_SEED = 42
TEXT_COLUMN = 'abstractText'
LABEL_COLUMN = 'condition'

# Load data
print(f"📂 Loading data from {INPUT_FILE}...")
df = pd.read_csv(INPUT_FILE)
print(f"✅ Loaded {len(df)} samples")

# Find and remove duplicates
cleaned_df, duplicate_info = find_and_remove_duplicates(df, TEXT_COLUMN, LABEL_COLUMN)

# Save duplicate info
duplicate_info_file = OUTPUT_FILE.replace('.json', '_duplicates_removed.json')
with open(duplicate_info_file, 'w') as f:
    json.dump(duplicate_info, f, indent=2)
print(f"💾 Saved duplicate info to {duplicate_info_file}")

# Preprocess cleaned data
X, labels, sample_ids, vectorizer = preprocess_clean_data(
    cleaned_df, TEXT_COLUMN, LABEL_COLUMN, MAX_FEATURES
)

# Run K-medoids selection
selected_indices, selected_sample_ids = balanced_kmedoids_selection(
    X, labels, sample_ids, TRAIN_SIZE, CLASS_BALANCE, RANDOM_SEED
)

# Save results
results = {
    'selected_ids': selected_sample_ids.tolist(),
    'method': 'clean_kmedoids',
    'parameters': {
        'train_size': TRAIN_SIZE,
        'max_features': MAX_FEATURES,
        'class_balance': CLASS_BALANCE,
        'random_seed': RANDOM_SEED,
        'duplicates_removed': len(duplicate_info),
        'original_size': len(df),
        'cleaned_size': len(cleaned_df)
    }
}

with open(OUTPUT_FILE, 'w') as f:
    json.dump(results, f, indent=2)

print(f"\n💾 Results saved to {OUTPUT_FILE}")
print("="*80)
print("📋 SUMMARY")
print("="*80)
print(f"📊 Original dataset: {len(df)} samples")
print(f"🗑️  Duplicates removed: {len(duplicate_info)} groups ({len(df) - len(cleaned_df)} samples)")
print(f"🧹 Cleaned dataset: {len(cleaned_df)} samples")
print(f"🎯 Selected subset: {len(selected_sample_ids)} samples")
print(f"⚙️  Selection method: K-medoids with {CLASS_BALANCE} class balance")
print(f"✅ Clean selection complete!")

# Display some statistics
if duplicate_info:
    print(f"\n🔍 DUPLICATE ANALYSIS:")
    print(f"   Found {len(duplicate_info)} text groups with conflicting labels")
    print(f"   These represent genuine labeling inconsistencies in the dataset")
    print(f"   Removing them ensures clean training data")
else:
    print(f"\n✅ NO DUPLICATES FOUND:")
    print(f"   Dataset appears to be clean of text-label conflicts")
