{"cells": [{"cell_type": "code", "execution_count": 1, "id": "22124ada", "metadata": {}, "outputs": [], "source": ["# Cell 1: Import and setup\n", "import pandas as pd\n", "import numpy as np\n", "import sklearn.feature_extraction.text\n", "import sklearn.linear_model\n", "from collections import defaultdict, Counter\n", "import scipy.sparse\n", "import cleanlab\n", "from cleanlab.classification import CleanLearning\n", "import joblib\n", "import os\n", "import re\n", "import string\n", "from typing import Dict, List, Tuple, Set\n"]}, {"cell_type": "code", "execution_count": 2, "id": "8ecd1cca", "metadata": {}, "outputs": [], "source": ["# Cell 2: Define the DuplicateAnalyzer class\n", "class DuplicateAnalyzer:\n", "    def __init__(self, input_dir: str = \"\", train_file: str = \"medical_tc_train.csv\"):\n", "        self.input_dir = input_dir\n", "        self.train_file = train_file\n", "        self.df = None\n", "        self.cleaned_texts = None\n", "        self.vectorizer = None\n", "        self.X_vec = None\n", "        self.duplicate_groups = None\n", "        self.cleanlab_issues = None\n", "        \n", "    def clean_text(self, text):\n", "        \"\"\"Clean text using the same method as preprocess_medical.py\"\"\"\n", "        if not isinstance(text, str):\n", "            return \"\"\n", "        text = text.lower()\n", "        text = re.sub(r'\\@\\w+|\\#', '', text)\n", "        text = re.sub(r'[%s]' % re.escape(string.punctuation), '', text)\n", "        text = re.sub(r'\\d+', '', text)\n", "        text = re.sub(r'\\s+', ' ', text).strip()\n", "        return text\n", "    \n", "    def load_and_preprocess_data(self):\n", "        \"\"\"Load and preprocess the medical data\"\"\"\n", "        print(\"Loading and preprocessing data...\")\n", "        \n", "        # Load data\n", "        train_csv_path = os.path.join(self.input_dir, self.train_file)\n", "        self.df = pd.read_csv(train_csv_path)\n", "        print(f\"Loaded {len(self.df)} samples\")\n", "        \n", "        # Clean texts\n", "        self.cleaned_texts = [self.clean_text(text) for text in self.df[\"medical_abstract\"].values]\n", "        \n", "        # Create TF-IDF vectors\n", "        self.vectorizer = sklearn.feature_extraction.text.TfidfVectorizer(\n", "            max_features=10000,\n", "            stop_words='english'\n", "        )\n", "        self.X_vec = self.vectorizer.fit_transform(self.cleaned_texts)\n", "        print(f\"Vectorization complete. Shape: {self.X_vec.shape}\")\n", "    \n", "    def find_exact_text_duplicates(self):\n", "        \"\"\"Find groups of samples with identical cleaned text\"\"\"\n", "        print(\"Finding exact text duplicates...\")\n", "        \n", "        text_to_indices = defaultdict(list)\n", "        for idx, text in enumerate(self.cleaned_texts):\n", "            if text.strip():\n", "                text_to_indices[text].append(idx)\n", "        \n", "        duplicate_groups = {text: indices for text, indices in text_to_indices.items() \n", "                          if len(indices) > 1}\n", "        \n", "        self.duplicate_groups = duplicate_groups\n", "        \n", "        print(f\"Found {len(duplicate_groups)} unique texts with duplicates\")\n", "        total_duplicate_samples = sum(len(indices) for indices in duplicate_groups.values())\n", "        print(f\"Total samples involved in duplicates: {total_duplicate_samples}\")\n", "        \n", "        return duplicate_groups\n", "    \n", "    def analyze_duplicate_labels(self):\n", "        \"\"\"Analyze label distribution within duplicate groups\"\"\"\n", "        if self.duplicate_groups is None:\n", "            self.find_exact_text_duplicates()\n", "        \n", "        print(\"Analyzing label distribution in duplicate groups...\")\n", "        \n", "        analysis = {\n", "            'conflicting_groups': [],\n", "            'consistent_groups': [],\n", "            'conflicting_indices': set(),\n", "            'total_conflicting_samples': 0\n", "        }\n", "        \n", "        for text, indices in self.duplicate_groups.items():\n", "            labels = [self.df.iloc[idx]['condition_label'] for idx in indices]\n", "            unique_labels = set(labels)\n", "            \n", "            group_info = {\n", "                'text_preview': text[:100] + \"...\" if len(text) > 100 else text,\n", "                'indices': indices,\n", "                'labels': labels,\n", "                'unique_labels': list(unique_labels),\n", "                'sample_count': len(indices)\n", "            }\n", "            \n", "            if len(unique_labels) > 1:\n", "                analysis['conflicting_groups'].append(group_info)\n", "                analysis['conflicting_indices'].update(indices)\n", "            else:\n", "                analysis['consistent_groups'].append(group_info)\n", "        \n", "        analysis['total_conflicting_samples'] = len(analysis['conflicting_indices'])\n", "        \n", "        print(f\"Groups with conflicting labels: {len(analysis['conflicting_groups'])}\")\n", "        print(f\"Groups with consistent labels: {len(analysis['consistent_groups'])}\")\n", "        print(f\"Total samples with conflicting duplicate labels: {analysis['total_conflicting_samples']}\")\n", "        \n", "        return analysis\n", "    \n", "    def run_cleanlab_analysis(self, num_folds: int = 5):\n", "        \"\"\"Run cleanlab to find label issues\"\"\"\n", "        print(f\"Running cleanlab analysis with {num_folds} folds...\")\n", "        \n", "        # Prepare data\n", "        y_original = self.df[\"condition_label\"].values\n", "        unique_y = np.unique(y_original)\n", "        label_map = {old: new for new, old in enumerate(unique_y)}\n", "        y_zero_indexed = np.array([label_map[y] for y in y_original])\n", "        \n", "        # Initialize cleanlab\n", "        clf = sklearn.linear_model.LogisticRegression(solver='liblinear', random_state=42)\n", "        cl = CleanLearning(clf, seed=42, verbose=True, cv_n_folds=num_folds)\n", "        \n", "        # Find label issues\n", "        issues = cl.find_label_issues(self.X_vec, y_zero_indexed)\n", "        issues['original_index'] = issues.index\n", "        issues['original_label'] = y_original\n", "        issues['text_preview'] = [text[:100] + \"...\" if len(text) > 100 else text \n", "                                for text in self.cleaned_texts]\n", "        \n", "        self.cleanlab_issues = issues\n", "        \n", "        print(f\"Cleanlab found {issues['is_label_issue'].sum()} label issues\")\n", "        print(f\"Mean label quality score: {issues['label_quality'].mean():.4f}\")\n", "        \n", "        return issues\n", "    \n", "    def compare_cleanlab_vs_duplicates(self, duplicate_analysis, cleanlab_issues):\n", "        \"\"\"Compare cleanlab findings with true duplicate analysis\"\"\"\n", "        print(\"Comparing cleanlab results with true duplicate analysis...\")\n", "        \n", "        cleanlab_flagged = set(cleanlab_issues[cleanlab_issues['is_label_issue']].index.tolist())\n", "        true_conflicts = duplicate_analysis['conflicting_indices']\n", "        \n", "        overlap = cleanlab_flagged.intersection(true_conflicts)\n", "        cleanlab_only = cleanlab_flagged - true_conflicts\n", "        duplicates_only = true_conflicts - cleanlab_flagged\n", "        \n", "        comparison = {\n", "            'cleanlab_flagged_count': len(cleanlab_flagged),\n", "            'true_conflicts_count': len(true_conflicts),\n", "            'overlap_count': len(overlap),\n", "            'cleanlab_only_count': len(cleanlab_only),\n", "            'duplicates_only_count': len(duplicates_only),\n", "            'overlap_indices': overlap,\n", "            'cleanlab_only_indices': cleanlab_only,\n", "            'duplicates_only_indices': duplicates_only\n", "        }\n", "        \n", "        # Calculate metrics\n", "        if len(true_conflicts) > 0:\n", "            comparison['recall'] = len(overlap) / len(true_conflicts)\n", "        else:\n", "            comparison['recall'] = None\n", "            \n", "        if len(cleanlab_flagged) > 0:\n", "            comparison['precision'] = len(overlap) / len(cleanlab_flagged)\n", "        else:\n", "            comparison['precision'] = None\n", "        \n", "        print(f\"Cleanlab flagged: {len(cleanlab_flagged)} samples\")\n", "        print(f\"True conflicting duplicates: {len(true_conflicts)} samples\")\n", "        print(f\"Overlap: {len(overlap)} samples\")\n", "        print(f\"Cleanlab-only flags: {len(cleanlab_only)} samples\")\n", "        print(f\"Missed true conflicts: {len(duplicates_only)} samples\")\n", "        \n", "        if comparison['recall'] is not None:\n", "            print(f\"Recall: {comparison['recall']:.2%}\")\n", "        if comparison['precision'] is not None:\n", "            print(f\"Precision: {comparison['precision']:.2%}\")\n", "        \n", "        return comparison\n"]}, {"cell_type": "code", "execution_count": 4, "id": "80b7f107", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading and preprocessing data...\n", "Loaded 11550 samples\n", "Vectorization complete. Shape: (11550, 10000)\n"]}], "source": ["# Cell 3: Run the analysis\n", "# Initialize analyzer - UPDATE THESE PATHS\n", "analyzer = DuplicateAnalyzer(\n", "    input_dir=\".\",  # Current directory, or specify full path\n", "    train_file=\"medical_tc_train.csv\"  # Your CSV file name\n", ")\n", "\n", "# Load and preprocess data\n", "analyzer.load_and_preprocess_data()"]}, {"cell_type": "code", "execution_count": 5, "id": "0b0e76f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finding exact text duplicates...\n", "Found 1956 unique texts with duplicates\n", "Total samples involved in duplicates: 4062\n", "Analyzing label distribution in duplicate groups...\n", "Groups with conflicting labels: 1956\n", "Groups with consistent labels: 0\n", "Total samples with conflicting duplicate labels: 4062\n"]}], "source": ["# Cell 4: Find duplicates\n", "duplicate_groups = analyzer.find_exact_text_duplicates()\n", "duplicate_analysis = analyzer.analyze_duplicate_labels()"]}, {"cell_type": "code", "execution_count": 6, "id": "b4c17ec5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running cleanlab analysis with 3 folds...\n", "Computing out of sample predicted probabilites via 3-fold cross validation. May take a while ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/venv/lib/python3.12/site-packages/sklearn/utils/deprecation.py:151: FutureWarning: 'force_all_finite' was renamed to 'ensure_all_finite' in 1.6 and will be removed in 1.8.\n", "  warnings.warn(\n", "/Users/<USER>/venv/lib/python3.12/site-packages/sklearn/utils/deprecation.py:151: FutureWarning: 'force_all_finite' was renamed to 'ensure_all_finite' in 1.6 and will be removed in 1.8.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Using predicted probabilities to identify label issues ...\n", "Identified 3864 examples with label issues.\n", "Cleanlab found 3864 label issues\n", "Mean label quality score: 0.4200\n"]}], "source": ["# Cell 5: Run cleanlab\n", "cleanlab_issues = analyzer.run_cleanlab_analysis(num_folds=3)"]}, {"cell_type": "code", "execution_count": 7, "id": "ecd7a789", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Comparing cleanlab results with true duplicate analysis...\n", "Cleanlab flagged: 3864 samples\n", "True conflicting duplicates: 4062 samples\n", "Overlap: 2327 samples\n", "Cleanlab-only flags: 1537 samples\n", "Missed true conflicts: 1735 samples\n", "Recall: 57.29%\n", "Precision: 60.22%\n"]}], "source": ["# Cell 6: Compare results\n", "comparison = analyzer.compare_cleanlab_vs_duplicates(duplicate_analysis, cleanlab_issues)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "8fc4b533", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "SUMMARY RESULTS\n", "============================================================\n", "Total samples: 11550\n", "Duplicate groups: 1956\n", "Conflicting duplicate samples: 4062\n", "Cleanlab flagged samples: 3864\n", "Overlap (cleanlab caught duplicates): 2327\n", "\n", "Recall (% of duplicates caught by cleanlab): 57.3%\n", "Precision (% of cleanlab flags that are duplicates): 60.2%\n"]}], "source": ["# Cell 7: Show detailed results\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"SUMMARY RESULTS\")\n", "print(\"=\"*60)\n", "print(f\"Total samples: {len(analyzer.df)}\")\n", "print(f\"Duplicate groups: {len(duplicate_groups)}\")\n", "print(f\"Conflicting duplicate samples: {duplicate_analysis['total_conflicting_samples']}\")\n", "print(f\"Cleanlab flagged samples: {comparison['cleanlab_flagged_count']}\")\n", "print(f\"Overlap (cleanlab caught duplicates): {comparison['overlap_count']}\")\n", "\n", "if comparison['recall'] is not None:\n", "    print(f\"\\nRecall (% of duplicates caught by cleanlab): {comparison['recall']:.1%}\")\n", "if comparison['precision'] is not None:\n", "    print(f\"Precision (% of cleanlab flags that are duplicates): {comparison['precision']:.1%}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "id": "65ef6a95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "EXAMPLES OF CONFLICTING DUPLICATES:\n", "\n", "Example 1:\n", "Indices: [3, 11031]\n", "Labels: [1, 5]\n", "Text: lipolytic factors associated with murine and human cancer cachexia we have identified a lipolytic fa...\n", "Caught by cleanlab: 1/2 samples\n", "\n", "Example 2:\n", "Indices: [4, 3547, 9865]\n", "Labels: [3, 4, 5]\n", "Text: does carotid restenosis predict an increased risk of late symptoms stroke or death the identificatio...\n", "Caught by cleanlab: 1/3 samples\n", "\n", "Example 3:\n", "Indices: [7, 10311]\n", "Labels: [4, 5]\n", "Text: pharmacomechanical thrombolysis and angioplasty in the management of clotted hemodialysis grafts ear...\n", "Caught by cleanlab: 1/2 samples\n"]}], "source": ["# Cell 8: Show examples of conflicting duplicates\n", "print(f\"\\nEXAMPLES OF CONFLICTING DUPLICATES:\")\n", "for i, group in enumerate(duplicate_analysis['conflicting_groups'][:3]):\n", "    print(f\"\\nExample {i+1}:\")\n", "    print(f\"Indices: {group['indices']}\")\n", "    print(f\"Labels: {group['labels']}\")\n", "    print(f\"Text: {group['text_preview']}\")\n", "    \n", "    caught = [idx for idx in group['indices'] if idx in comparison['overlap_indices']]\n", "    print(f\"Caught by cleanlab: {len(caught)}/{len(group['indices'])} samples\")\n"]}, {"cell_type": "code", "execution_count": 10, "id": "86164176", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "EXAMPLES MISSED BY CLEANLAB:\n", "\n", "Index 3:\n", "Label: 1\n", "Text: lipolytic factors associated with murine and human cancer cachexia we have identified a lipolytic fa...\n", "\n", "Index 4:\n", "Label: 3\n", "Text: does carotid restenosis predict an increased risk of late symptoms stroke or death the identificatio...\n", "\n", "Index 8197:\n", "Label: 5\n", "Text: rapid histological changes in endomyocardial biopsy specimens after myocarditis the course and respo...\n"]}], "source": ["\n", "# Cell 9: Show examples missed by cleanlab\n", "if len(comparison['duplicates_only_indices']) > 0:\n", "    print(f\"\\nEXAMPLES MISSED BY CLEANLAB:\")\n", "    missed_sample = list(comparison['duplicates_only_indices'])[:3]\n", "    for idx in missed_sample:\n", "        print(f\"\\nIndex {idx}:\")\n", "        print(f\"Label: {analyzer.df.iloc[idx]['condition_label']}\")\n", "        print(f\"Text: {analyzer.cleaned_texts[idx][:100]}...\")"]}, {"cell_type": "code", "execution_count": null, "id": "d82f942a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}