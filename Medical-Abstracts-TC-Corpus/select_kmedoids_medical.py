# Cell 1: Import and setup (FIXED VERSION)
import pandas as pd
import numpy as np
import sklearn.feature_extraction.text
from sklearn_extra.cluster import KMedoids
from collections import defaultdict, Counter
import scipy.sparse
import json
import os
import re
import string
import time
import argparse
from typing import Dict, List, Tuple, Set

class DuplicateRemover:
    """Remove samples with true duplicate label conflicts"""
    
    def __init__(self):
        self.duplicate_groups = None
        self.conflicting_indices = set()
    
    def clean_text(self, text):
        """Clean text using the same method as preprocess_medical.py"""
        if not isinstance(text, str):
            return ""
        text = text.lower()
        text = re.sub(r'\@\w+|\#', '', text)
        text = re.sub(r'[%s]' % re.escape(string.punctuation), '', text)
        text = re.sub(r'\d+', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def find_conflicting_duplicates(self, texts, labels):
        """Find indices of samples that have conflicting labels for identical text"""
        print("Finding conflicting duplicates...")
        
        # Clean texts
        cleaned_texts = [self.clean_text(text) for text in texts]
        
        # Group by cleaned text
        text_to_indices = defaultdict(list)
        for idx, text in enumerate(cleaned_texts):
            if text.strip():
                text_to_indices[text].append(idx)
        
        # Find groups with conflicting labels
        conflicting_indices = set()
        conflicting_groups = []
        
        for text, indices in text_to_indices.items():
            if len(indices) > 1:
                group_labels = [labels[idx] for idx in indices]
                unique_labels = set(group_labels)
                
                if len(unique_labels) > 1:
                    # This group has conflicting labels
                    conflicting_indices.update(indices)
                    conflicting_groups.append({
                        'text': text[:100] + "..." if len(text) > 100 else text,
                        'indices': indices,
                        'labels': group_labels
                    })
        
        self.conflicting_indices = conflicting_indices
        self.duplicate_groups = conflicting_groups
        
        print(f"Found {len(conflicting_groups)} groups with conflicting labels")
        print(f"Total conflicting samples to remove: {len(conflicting_indices)}")
        
        return conflicting_indices
    
    def detect_duplicates(self, texts, labels):
        """Alias for find_conflicting_duplicates for compatibility"""
        return self.find_conflicting_duplicates(texts, labels)

class KMedoidsSelector:
    """K-Medoids based sample selection with duplicate removal"""
    
    def __init__(self, random_seed=42):
        self.random_seed = random_seed
        self.vectorizer = None
        self.duplicate_remover = DuplicateRemover()
    
    def load_data(self, preprocessed_data_dir, input_dir="", train_file="medical_tc_train.csv"):
        """Load preprocessed data"""
        print("Loading preprocessed data...")
        
        # Load sparse matrix from .npz file
        X_train_npz = np.load(os.path.join(preprocessed_data_dir, 'X_train_full_vec.npz'))
        X_train = scipy.sparse.csr_matrix(
            (X_train_npz['data'], X_train_npz['indices'], X_train_npz['indptr']),
            shape=X_train_npz['shape']
        )
        
        # Load labels
        y_train = np.load(os.path.join(preprocessed_data_dir, 'y_train_full.npy'))
        
        # Load original texts for duplicate detection
        # Try to find the CSV file in multiple locations
        csv_paths_to_try = [
            os.path.join(preprocessed_data_dir, train_file),
            os.path.join(input_dir, train_file),
            train_file  # Current directory
        ]
        
        train_df = None
        for csv_path in csv_paths_to_try:
            if os.path.exists(csv_path):
                train_df = pd.read_csv(csv_path)
                print(f"Found training CSV at: {csv_path}")
                break
        
        if train_df is None:
            raise FileNotFoundError(f"Could not find {train_file} in any of these locations: {csv_paths_to_try}")
        
        texts = train_df['medical_abstract'].values
        
        print(f"Loaded {X_train.shape[0]} training samples with {X_train.shape[1]} features")
        print(f"Sparse matrix density: {X_train.nnz / (X_train.shape[0] * X_train.shape[1]):.4f}")
        
        return X_train, y_train, texts
    
    def remove_duplicates(self, X_train, y_train, texts):
        """Remove conflicting duplicates from the training data."""
        # Detect duplicates (FIXED: use the correct method name)
        self.duplicate_remover.detect_duplicates(texts, y_train)
        
        # Get conflicting indices
        conflicting_indices = self.duplicate_remover.conflicting_indices
        
        if not conflicting_indices:
            print("No conflicting duplicates found.")
            # Return original data with all indices
            return X_train, y_train, np.arange(X_train.shape[0])
        
        # Create mask for non-conflicting samples
        all_indices = set(range(X_train.shape[0]))
        clean_indices = sorted(all_indices - conflicting_indices)
        clean_indices = np.array(clean_indices)
        
        # Filter the data
        X_clean = X_train[clean_indices]
        y_clean = y_train[clean_indices]
        
        print(f"Removed {len(conflicting_indices)} conflicting samples.")
        print(f"Clean dataset size: {len(clean_indices)}")
    
        return X_clean, y_clean, clean_indices
    
    def select_samples_kmedoids(self, X, y, original_indices, train_size, class_balance="equal", extra_frac=0.5):
        """Select samples using K-Medoids clustering"""
        print(f"Selecting {train_size} samples using K-Medoids...")
        
        unique_labels = np.unique(y)
        n_classes = len(unique_labels)
        
        if class_balance == "equal":
            base_samples_per_class = train_size // n_classes
            extra_samples = train_size % n_classes
            samples_per_class = [base_samples_per_class] * n_classes
            # Distribute extra samples to first few classes
            for i in range(extra_samples):
                samples_per_class[i] += 1
        else:
            # Proportional to class distribution
            class_counts = Counter(y)
            total_samples = len(y)
            samples_per_class = []
            for label in unique_labels:
                prop = class_counts[label] / total_samples
                samples_per_class.append(max(1, int(train_size * prop)))
        
        print(f"Target samples per class: {dict(zip(unique_labels, samples_per_class))}")
        
        selected_indices = []
        
        for i, label in enumerate(unique_labels):
            target_samples = samples_per_class[i]
            
            # Get indices for this class
            class_mask = (y == label)
            class_indices = np.where(class_mask)[0]
            X_class = X[class_indices]
            
            print(f"\nProcessing class {label}: {len(class_indices)} samples, target: {target_samples}")
            
            if len(class_indices) <= target_samples:
                # Take all samples if we have fewer than needed
                selected_class_indices = class_indices
                print(f"  Taking all {len(class_indices)} samples (fewer than target)")
            else:
                # Use K-Medoids clustering
                try:
                    # Determine number of clusters based on target samples and extra_frac
                    actual_clusters = min(target_samples, len(class_indices))
                    if extra_frac > 0:
                        actual_clusters = min(int(target_samples * (1 + extra_frac)), len(class_indices))
                    
                    print(f"  Running K-Medoids with {actual_clusters} clusters")
                    
                    start_time = time.time()
                    
                    # Convert sparse matrix to dense for K-Medoids
                    if scipy.sparse.issparse(X_class):
                        X_class_dense = X_class.toarray()
                    else:
                        X_class_dense = X_class
                    
                    # Run K-Medoids
                    kmedoids = KMedoids(
                        n_clusters=actual_clusters, 
                        init="k-medoids++", 
                        random_state=self.random_seed,
                        metric='euclidean'
                    )
                    kmedoids.fit(X_class_dense)
                    
                    # Get medoid indices within this class
                    medoid_indices_in_class = kmedoids.medoid_indices_
                    
                    # Map back to global indices
                    selected_class_indices = class_indices[medoid_indices_in_class]
                    
                    # If we got more than needed, randomly sample down
                    if len(selected_class_indices) > target_samples:
                        np.random.seed(self.random_seed + i)  # Different seed per class
                        selected_class_indices = np.random.choice(
                            selected_class_indices, 
                            target_samples, 
                            replace=False
                        )
                    
                    print(f"  K-Medoids completed in {time.time() - start_time:.2f} seconds")
                    print(f"  Selected {len(selected_class_indices)} samples")
                    
                except Exception as e:
                    print(f"  Error during K-Medoids for class {label}: {e}")
                    print(f"  Falling back to random selection")
                    
                    # Fallback to random selection
                    np.random.seed(self.random_seed + i)
                    selected_class_indices = np.random.choice(
                        class_indices, 
                        target_samples, 
                        replace=False
                    )
            
            selected_indices.extend(selected_class_indices)
        
        # Convert to numpy array and map back to original indices
        selected_indices = np.array(selected_indices)
        original_selected_indices = original_indices[selected_indices]
        
        print(f"\nTotal selected samples: {len(original_selected_indices)}")
        
        # Verify class distribution
        selected_labels = y[selected_indices]
        final_class_counts = Counter(selected_labels)
        print("Final class distribution:")
        for label in unique_labels:
            print(f"  Class {label}: {final_class_counts[label]} samples")
        
        return original_selected_indices
    
def main():
    parser = argparse.ArgumentParser(description='K-Medoids sample selection with duplicate removal')
    parser.add_argument('--preprocessed_data_dir', type=str, required=True, help='Directory containing preprocessed data')
    parser.add_argument('--input_dir', type=str, default='', help='Directory containing original CSV files')
    parser.add_argument('--train_file', type=str, default='medical_tc_train.csv', help='Name of training CSV file')
    parser.add_argument('--output_file', type=str, default='selected_train_ids_kmedoids.json', help='Output JSON file')
    parser.add_argument('--train_size', type=int, required=True, help='Number of samples to select')
    parser.add_argument('--random_seed', type=int, default=42, help='Random seed')
    parser.add_argument('--class_balance', type=str, choices=['equal', 'proportional'], default='equal', help='Class balancing strategy')
    parser.add_argument('--extra_frac', type=float, default=0.5, help='Extra fraction for initial clustering')
    
    args = parser.parse_args()
    
    print("="*80)
    print("K-MEDOIDS SAMPLE SELECTION WITH DUPLICATE REMOVAL")
    print("="*80)
    print(f"Preprocessed data directory: {args.preprocessed_data_dir}")
    print(f"Input directory: {args.input_dir}")
    print(f"Training file: {args.train_file}")
    print(f"Output file: {args.output_file}")
    print(f"Target train size: {args.train_size}")
    print(f"Random seed: {args.random_seed}")
    print(f"Class balance: {args.class_balance}")
    print(f"Extra fraction: {args.extra_frac}")
    print("="*80)
    
    # Initialize selector
    selector = KMedoidsSelector(random_seed=args.random_seed)
    
    # Load data
    X_train, y_train, texts = selector.load_data(args.preprocessed_data_dir, args.input_dir, args.train_file)
    
    # Remove conflicting duplicates
    X_clean, y_clean, clean_indices = selector.remove_duplicates(X_train, y_train, texts)
    
    # Select samples using K-Medoids
    selected_indices = selector.select_samples_kmedoids(
        X_clean, y_clean, clean_indices, 
        args.train_size, args.class_balance, args.extra_frac
    )
    
    # Save results
    result = {
        'selected_ids': selected_indices.tolist(),
        'total_selected': len(selected_indices),
        'method': 'kmedoids_with_duplicate_removal',
        'parameters': {
            'train_size': args.train_size,
            'random_seed': args.random_seed,
            'class_balance': args.class_balance,
            'extra_frac': args.extra_frac
        },
        'duplicate_info': {
            'conflicting_groups': len(selector.duplicate_remover.duplicate_groups) if selector.duplicate_remover.duplicate_groups else 0,
            'conflicting_samples_removed': len(selector.duplicate_remover.conflicting_indices)
        }
    }
    
    with open(args.output_file, 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\nResults saved to {args.output_file}")
    print("="*80)

if __name__ == "__main__":
    main()