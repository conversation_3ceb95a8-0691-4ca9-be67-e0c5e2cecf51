#!/usr/bin/env python3
"""
Clean dataset by removing duplicates, then run K-medoids selection.

This script:
1. Loads the raw medical_train.csv
2. Identifies and removes samples with identical text but different labels
3. Preprocesses the cleaned data (TF-IDF vectorization)
4. Runs K-medoids clustering for balanced selection
5. Saves the selected subset
"""

import pandas as pd
import numpy as np
import json
import argparse
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn_extra.cluster import KMedoids
import scipy.sparse
from collections import Counter
import time

def find_and_remove_duplicates(df, text_column='abstractText', label_column='condition'):
    """
    Find samples with identical text but different labels and remove them.
    
    Returns:
        cleaned_df: DataFrame with duplicates removed
        duplicate_info: Information about removed duplicates
    """
    print("Finding duplicates (identical text with different labels)...")
    
    # Group by text to find duplicates
    text_groups = df.groupby(text_column)
    
    duplicate_info = []
    indices_to_remove = set()
    
    for text, group in text_groups:
        if len(group) > 1:  # Multiple samples with same text
            unique_labels = group[label_column].unique()
            if len(unique_labels) > 1:  # Different labels for same text
                # This is a true duplicate - record info and mark for removal
                duplicate_info.append({
                    'text': text[:200] + "..." if len(text) > 200 else text,
                    'labels': list(unique_labels),
                    'sample_ids': list(group.index),
                    'count': len(group)
                })
                # Remove all instances of this duplicate text
                indices_to_remove.update(group.index)
    
    print(f"Found {len(duplicate_info)} duplicate text groups with different labels")
    print(f"Removing {len(indices_to_remove)} samples total")
    
    if duplicate_info:
        print("\nDuplicate examples:")
        for i, dup in enumerate(duplicate_info[:3]):
            print(f"  {i+1}. Text: {dup['text']}")
            print(f"     Labels: {dup['labels']}")
            print(f"     Count: {dup['count']}")
    
    # Create cleaned dataframe
    cleaned_df = df.drop(indices_to_remove).reset_index(drop=True)
    
    print(f"\nDataset size: {len(df)} -> {len(cleaned_df)} (removed {len(df) - len(cleaned_df)} samples)")
    
    return cleaned_df, duplicate_info

def preprocess_data(df, text_column='abstractText', label_column='condition', max_features=10000):
    """
    Preprocess the cleaned data with TF-IDF vectorization.
    """
    print(f"Preprocessing data with TF-IDF (max_features={max_features})...")
    
    # Extract texts and labels
    texts = df[text_column].values
    labels = df[label_column].values
    sample_ids = df.index.values  # Use DataFrame index as sample IDs
    
    # TF-IDF vectorization
    vectorizer = TfidfVectorizer(
        max_features=max_features,
        stop_words='english',
        lowercase=True,
        ngram_range=(1, 2),  # Include bigrams
        min_df=2,  # Ignore terms that appear in less than 2 documents
        max_df=0.95  # Ignore terms that appear in more than 95% of documents
    )
    
    X = vectorizer.fit_transform(texts)
    
    print(f"Vectorized to {X.shape[0]} samples x {X.shape[1]} features")
    print(f"Matrix sparsity: {(1 - X.nnz / (X.shape[0] * X.shape[1])):.3f}")
    
    return X, labels, sample_ids, vectorizer

def balanced_kmedoids_selection(X, labels, sample_ids, target_size, class_balance='equal', random_seed=42):
    """
    Select a balanced subset using K-medoids clustering.
    
    Args:
        X: Feature matrix
        labels: Class labels
        sample_ids: Sample identifiers
        target_size: Total number of samples to select
        class_balance: 'equal' or 'proportional'
        random_seed: Random seed for reproducibility
    """
    print(f"Running balanced K-medoids selection for {target_size} samples...")
    
    unique_classes = np.unique(labels)
    num_classes = len(unique_classes)
    
    print(f"Classes: {unique_classes}")
    print(f"Original class distribution:")
    for cls in unique_classes:
        count = np.sum(labels == cls)
        print(f"  Class {cls}: {count} samples")
    
    # Determine per-class target sizes
    if class_balance == 'equal':
        per_class_size = target_size // num_classes
        class_sizes = {cls: per_class_size for cls in unique_classes}
        # Distribute remaining samples
        remaining = target_size - (per_class_size * num_classes)
        for i in range(remaining):
            cls = unique_classes[i % num_classes]
            class_sizes[cls] += 1
    else:  # proportional
        class_counts = {cls: np.sum(labels == cls) for cls in unique_classes}
        total_samples = len(labels)
        class_sizes = {
            cls: max(1, int(target_size * (count / total_samples)))
            for cls, count in class_counts.items()
        }
        # Adjust to ensure exact target size
        current_total = sum(class_sizes.values())
        if current_total != target_size:
            # Add/remove from largest classes
            diff = target_size - current_total
            sorted_classes = sorted(class_counts.items(), key=lambda x: x[1], reverse=True)
            for i in range(abs(diff)):
                cls = sorted_classes[i % len(sorted_classes)][0]
                class_sizes[cls] += 1 if diff > 0 else -1
    
    print(f"Target class distribution:")
    for cls in unique_classes:
        print(f"  Class {cls}: {class_sizes[cls]} samples")
    
    # Select samples for each class using K-medoids
    selected_indices = []
    
    for cls in unique_classes:
        target_count = class_sizes[cls]
        if target_count <= 0:
            continue
            
        # Get indices for this class
        class_indices = np.where(labels == cls)[0]
        available_count = len(class_indices)
        
        print(f"Selecting {target_count} from {available_count} samples of class {cls}...")
        
        if target_count >= available_count:
            # Take all samples if we need more than available
            selected_indices.extend(class_indices)
        else:
            # Use K-medoids to select representative samples
            X_class = X[class_indices]
            
            # Convert to dense if sparse (K-medoids doesn't handle sparse well)
            if scipy.sparse.issparse(X_class):
                X_class_dense = X_class.toarray()
            else:
                X_class_dense = X_class
            
            # Run K-medoids
            kmedoids = KMedoids(n_clusters=target_count, init="k-medoids++", random_state=random_seed)
            kmedoids.fit(X_class_dense)
            
            # Get selected indices (medoids)
            selected_class_indices = class_indices[kmedoids.medoid_indices_]
            selected_indices.extend(selected_class_indices)
    
    selected_indices = np.array(selected_indices)
    selected_sample_ids = sample_ids[selected_indices]
    
    print(f"Selected {len(selected_indices)} samples total")
    print("Final class distribution:")
    selected_labels = labels[selected_indices]
    for cls in unique_classes:
        count = np.sum(selected_labels == cls)
        print(f"  Class {cls}: {count} samples")
    
    return selected_indices, selected_sample_ids

def main():
    parser = argparse.ArgumentParser(description='Clean dataset and run K-medoids selection')
    parser.add_argument('--input_file', default='medical_train.csv', help='Input CSV file')
    parser.add_argument('--output_file', default='selected_train_ids_clean_kmedoids.json', help='Output JSON file')
    parser.add_argument('--train_size', type=int, default=1000, help='Target training set size')
    parser.add_argument('--max_features', type=int, default=10000, help='Maximum TF-IDF features')
    parser.add_argument('--class_balance', choices=['equal', 'proportional'], default='equal', help='Class balancing strategy')
    parser.add_argument('--random_seed', type=int, default=42, help='Random seed')
    parser.add_argument('--text_column', default='abstractText', help='Text column name')
    parser.add_argument('--label_column', default='condition', help='Label column name')
    
    args = parser.parse_args()
    
    print("="*80)
    print("CLEAN DATASET + K-MEDOIDS SELECTION")
    print("="*80)
    
    # Load data
    print(f"Loading data from {args.input_file}...")
    df = pd.read_csv(args.input_file)
    print(f"Loaded {len(df)} samples")
    
    # Find and remove duplicates
    cleaned_df, duplicate_info = find_and_remove_duplicates(
        df, args.text_column, args.label_column
    )
    
    # Save duplicate info
    duplicate_info_file = args.output_file.replace('.json', '_duplicates_removed.json')
    with open(duplicate_info_file, 'w') as f:
        json.dump(duplicate_info, f, indent=2)
    print(f"Saved duplicate info to {duplicate_info_file}")
    
    # Preprocess cleaned data
    X, labels, sample_ids, vectorizer = preprocess_data(
        cleaned_df, args.text_column, args.label_column, args.max_features
    )
    
    # Run K-medoids selection
    selected_indices, selected_sample_ids = balanced_kmedoids_selection(
        X, labels, sample_ids, args.train_size, args.class_balance, args.random_seed
    )
    
    # Save results
    results = {
        'selected_ids': selected_sample_ids.tolist(),
        'method': 'clean_kmedoids',
        'parameters': {
            'train_size': args.train_size,
            'max_features': args.max_features,
            'class_balance': args.class_balance,
            'random_seed': args.random_seed,
            'duplicates_removed': len(duplicate_info),
            'original_size': len(df),
            'cleaned_size': len(cleaned_df)
        }
    }
    
    with open(args.output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nResults saved to {args.output_file}")
    print("="*80)
    print("SUMMARY")
    print("="*80)
    print(f"Original dataset: {len(df)} samples")
    print(f"Duplicates removed: {len(duplicate_info)} groups ({len(df) - len(cleaned_df)} samples)")
    print(f"Cleaned dataset: {len(cleaned_df)} samples")
    print(f"Selected subset: {len(selected_sample_ids)} samples")
    print(f"Selection method: K-medoids with {args.class_balance} class balance")

if __name__ == "__main__":
    main()
