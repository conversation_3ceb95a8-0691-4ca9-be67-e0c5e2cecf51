TRAIN_SIZE = 25
!python Baseline/select_medical.py \
--preprocessed_data_dir="preprocessed_data/" \
  --output_file="Baseline/selected_train_ids.json" \
  --train_size={TRAIN_SIZE} \
  --random_seed=42 \
  --n_folds=10 \
  --outer_split_size=0.8

!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Baseline/selected_train_ids.json --metrics_output_file Baseline/results_selected_subset.json

import os
import json
import numpy as np
import matplotlib.pyplot as plt

train_ids_path = os.path.join('preprocessed_data', 'train_sample_ids.npy')
train_sample_ids = np.load(train_ids_path)
max_train_size = len(train_sample_ids)

max_train_size

fixed_small_sizes = [25, 60, 100, 200, 500, 1000]


results = []
for train_size in fixed_small_sizes:
    print(f'Running for train_size={train_size}')
    # Run selection
    !python select_medical.py \
      --preprocessed_data_dir="preprocessed_data/" \
      --output_file="selected_train_ids.json" \
      --train_size={train_size} \
      --random_seed=42 \
      --n_folds=10 \
      --outer_split_size=0.8

    # Run evaluation
    !python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json selected_train_ids.json --metrics_output_file results_selected_subset.json

    with open('results_selected_subset.json', 'r') as f:
        metrics = json.load(f)
    metrics['train_size'] = train_size
    results.append(metrics)

with open('train_size_vs_f1.json', 'w') as f:
    json.dump(results, f, indent=2)

print('Sweep complete. Results saved to train_size_vs_f1.json')

import json
import matplotlib.pyplot as plt

with open('train_size_vs_f1.json', 'r') as f:
    results = json.load(f)

train_sizes = [r['train_size'] for r in results]
f1_scores = [r['average_f1_macro'] for r in results]

plt.figure(figsize=(8,5))
plt.plot(train_sizes, f1_scores, marker='o')
plt.xscale('log')
plt.xlabel('Training Set Size (log scale)')
plt.ylabel('Average F1 Macro')
plt.title('F1 Macro vs. Training Set Size')
plt.grid(True, which='both', ls='--', alpha=0.5)
plt.tight_layout()
plt.show()

TRAIN_SIZE = 25 
!python Cleanlab/select_cleanlab_medical.py \
  --preprocessed_data_dir="preprocessed_data/" \
  --output_file="Cleanlab/selected_train_ids_cleanlab.json" \
  --train_size={TRAIN_SIZE} \
  --random_seed=42 \
  --n_folds=3 \
  --class_balance="equal" \
  --extra_frac=0.5

!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Cleanlab/selected_train_ids_cleanlab.json --metrics_output_file Cleanlab/results_selected_subset.json

!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Cleanlab/selected_train_ids_cleanlab.json --metrics_output_file Cleanlab/results_selected_subset.json

train_size_list = [25, 60, 100, 200, 500, 1000]

import json
import numpy as np

log_file = f'cleanlab_sweep_results_1.txt'

results = []
with open(log_file, 'w') as f:
    f.write("=" * 80 + "\n\n")
    
    for train_size in train_size_list:
        f.write("=" * 80 + "\n")
        
        output_fname = f"Cleanlab//selected_train_ids_cleanlab_{train_size}.json"
        !python Cleanlab/select_cleanlab_medical.py \
          --preprocessed_data_dir="preprocessed_data/" \
          --output_file={output_fname} \
          --train_size={train_size} \
          --random_seed=42 \
          --n_folds=3 \
          --class_balance="equal" \
          --extra_frac=0.5

        with open(output_fname, 'r') as rf:
            selected_data = json.load(rf)
            selected_ids = np.array(selected_data['selected_ids'])
            
        # Get label counts
        labels = np.load('preprocessed_data/y_train_full.npy')[selected_ids]
        unique, counts = np.unique(labels, return_counts=True)
        
        # Write class distribution
        f.write(f"\nSelected training set created with {train_size} samples.\n")
        for label, count in zip(unique, counts):
            f.write(f"  Label {label}: {count} samples\n")
        
        # Run evaluation
        !python eval_medical.py \
          --preprocessed_data_dir preprocessed_data/ \
          --selected_ids_json Cleanlab/selected_train_ids_cleanlab_{train_size}.json \
          --metrics_output_file Cleanlab/results_selected_subset.json
        
        # Store and log evaluation results
        with open('results_selected_subset.json', 'r') as rf:
            metrics = json.load(rf)
        
        f.write("--- Evaluation Results ---\n")
        # Safely access metrics with fallback values
        f.write(f"Individual F1 Macro Scores: {metrics.get('f1_macro_scores', metrics.get('individual_f1_scores', []))}\n")
        f.write(f"Average F1 Macro Score: {metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0)):.4f}\n")
        f.write(f"Standard Deviation of F1 Macro Scores: {metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0)):.4f}\n")
        
        # Add blank lines for readability
        f.write("\n\n")
        f.flush()  # Force write to disk

print(f'\nSweep complete! Detailed log saved to {log_file}')

import numpy as np
import matplotlib.pyplot as plt

# Original data
train_sizes_orig = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_orig = [0.0999, 0.1000, 0.1792, 0.3471, 0.4952, 0.5189, 0.5087]
std_devs_orig = [0.0000, 0.0002, 0.0467, 0.0155, 0.0099, 0.0075, 0.0000]

# Cleanlab data (including full dataset)
train_sizes_cleanlab = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_cleanlab = [0.1569, 0.3716, 0.5255, 0.5683, 0.5887, 0.6046, 0.5087]
std_devs_cleanlab = [0.0924, 0.1302, 0.0210, 0.0046, 0.0010, 0.0011, 0.0000]

plt.figure(figsize=(12, 8))

# Plot original data
plt.errorbar(train_sizes_orig, f1_scores_orig, yerr=std_devs_orig, 
             fmt='o-', capsize=5, label='Random Selection', color='blue')

# Plot cleanlab data
plt.errorbar(train_sizes_cleanlab, f1_scores_cleanlab, yerr=std_devs_cleanlab, 
             fmt='s-', capsize=5, label='Cleanlab Selection', color='red')

plt.xscale('log')
plt.grid(True, which="both", ls="-", alpha=0.2)

plt.xlabel('Training Set Size (log scale)')
plt.ylabel('F1 Macro Score')
plt.title('F1 Macro Score vs Training Size: Random vs Cleanlab Selection')
plt.legend()

# Add value labels for both curves
for x, y in zip(train_sizes_orig, f1_scores_orig):
    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                xytext=(0,10), ha='center', color='blue')
for x, y in zip(train_sizes_cleanlab, f1_scores_cleanlab):
    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                xytext=(0,-15), ha='center', color='red')

plt.tight_layout()
plt.show()

import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from mpl_toolkits.mplot3d import Axes3D

# Hàm load file .npz lưu cấu trúc CSR nhưng không dùng scipy
def load_sparse_npz_manual(filename):
    loader = np.load(filename)
    data    = loader['data']
    indices = loader['indices']
    indptr  = loader['indptr']
    shape   = tuple(loader['shape'])
    return data, indices, indptr, shape

# Load dữ liệu
data, indices, indptr, shape = load_sparse_npz_manual('preprocessed_data/X_train_full_vec.npz')
y_train_full = np.load('preprocessed_data/y_train_full.npy')

n_samples, n_features = shape

# Sample ngẫu nhiên
sample_size = min(3000, n_samples)
random_indices = np.random.choice(n_samples, size=sample_size, replace=False)

# Rebuild hàng dense cho các index đã chọn
X_sample = np.zeros((sample_size, n_features), dtype=float)
for out_row, i in enumerate(random_indices):
    start, end = indptr[i], indptr[i+1]
    cols  = indices[start:end]
    vals  = data   [start:end]
    X_sample[out_row, cols] = vals

y_sample = y_train_full[random_indices]

# Áp dụng PCA
print("Applying PCA to reduce dimensionality...")
pca = PCA(n_components=3)
X_pca = pca.fit_transform(X_sample)

print("Explained variance ratio:", pca.explained_variance_ratio_)
print(f"Total explained variance: {pca.explained_variance_ratio_.sum():.2f}")

# Vẽ 2D
unique_labels = np.unique(y_sample)
plt.figure(figsize=(12, 8))
for label in unique_labels:
    mask = (y_sample == label)
    plt.scatter(
        X_pca[mask, 0], X_pca[mask, 1],
        label=f'Class {label}',
        alpha=0.7, edgecolors='w'
    )
plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2f})')
plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2f})')
plt.title('2D PCA of Medical Abstracts TF-IDF Features')
plt.legend(title="Classes")
plt.grid(alpha=0.3)
plt.tight_layout()
plt.show()

import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from math import ceil, floor, sqrt

# Hàm load file .npz lưu cấu trúc CSR nhưng không dùng scipy
def load_sparse_npz_manual(filename):
    loader  = np.load(filename)
    data    = loader['data']
    indices = loader['indices']
    indptr  = loader['indptr']
    shape   = tuple(loader['shape'])
    return data, indices, indptr, shape

# Load dữ liệu
data, indices, indptr, shape = load_sparse_npz_manual('preprocessed_data/X_train_full_vec.npz')
y_train_full = np.load('preprocessed_data/y_train_full.npy')
n_samples, n_features = shape

# Sample ngẫu nhiên
sample_size = min(3000, n_samples)
random_indices = np.random.choice(n_samples, size=sample_size, replace=False)

# Rebuild dense sample
X_sample = np.zeros((sample_size, n_features), dtype=float)
for out_row, i in enumerate(random_indices):
    start, end = indptr[i], indptr[i+1]
    cols  = indices[start:end]
    vals  = data   [start:end]
    X_sample[out_row, cols] = vals
y_sample = y_train_full[random_indices]

# Áp dụng PCA
pca = PCA(n_components=2)
X_pca = pca.fit_transform(X_sample)

# Chuẩn bị subplot grid
unique_labels = np.unique(y_sample)
n_classes = len(unique_labels)
cols = int(ceil(sqrt(n_classes)))
rows = int(ceil(n_classes / cols))

fig, axes = plt.subplots(rows, cols, figsize=(4*cols, 4*rows), sharex=True, sharey=True)
axes = axes.flatten()

for ax, lbl in zip(axes, unique_labels):
    mask = (y_sample == lbl)
    ax.scatter(
        X_pca[mask, 0], X_pca[mask, 1],
        alpha=0.7, edgecolors='w'
    )
    ax.set_title(f'Class {lbl}')
    ax.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2f})')
    ax.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2f})')
    ax.grid(alpha=0.3)

# Ẩn các subplot thừa nếu có
for j in range(len(unique_labels), len(axes)):
    fig.delaxes(axes[j])

plt.tight_layout()
plt.show()


import numpy as np
import json
from scipy.sparse import csr_matrix
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA

# Load all sample IDs and Cleanlab selected IDs
train_sample_ids = np.load('preprocessed_data/train_sample_ids.npy')
with open('Cleanlab/selected_train_ids_cleanlab.json', 'r') as f:
    selected_data = json.load(f)

# Hỗ trợ cả 2 format: {"selected_ids": [...]} hoặc {label: [ids]}
if 'selected_ids' in selected_data:
    selected_ids = np.array(selected_data['selected_ids'])
else:
    selected_ids = []
    for v in selected_data.values():
        selected_ids.extend(v)
    selected_ids = np.array(selected_ids)

# Map selected_ids sang index trong train_sample_ids
selected_indices = np.where(np.isin(train_sample_ids, selected_ids))[0]

# Load TF-IDF vectors và labels
def load_sparse_csr(filename):
    loader = np.load(filename)
    return csr_matrix((loader['data'], loader['indices'], loader['indptr']), shape=loader['shape'])

X_train_full_vec = load_sparse_csr('preprocessed_data/X_train_full_vec.npz')
y_train_full = np.load('preprocessed_data/y_train_full.npy')

# Lấy vector và label của các điểm được chọn
X_selected = X_train_full_vec[selected_indices].toarray()
y_selected = y_train_full[selected_indices]

# PCA
pca = PCA(n_components=2)
X_pca = pca.fit_transform(X_selected)

# Vẽ scatter plot
plt.figure(figsize=(10, 7))
for label in np.unique(y_selected):
    mask = y_selected == label
    plt.scatter(X_pca[mask, 0], X_pca[mask, 1], label=f'Class {label}', alpha=0.7, edgecolors='w')
plt.xlabel('PC1')
plt.ylabel('PC2')
plt.title('PCA Visualization of Cleanlab Selected Samples')
plt.legend()
plt.grid(alpha=0.3)
plt.tight_layout()
plt.show()

import numpy as np
import json
from scipy.sparse import csr_matrix
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from math import ceil, sqrt

# Load all sample IDs và Cleanlab selected IDs
train_sample_ids = np.load('preprocessed_data/train_sample_ids.npy')
for train_size in train_size_list:
    path = f'Cleanlab/selected_train_ids_cleanlab_{train_size}.json'
    with open(path, 'r') as f:
        selected_data = json.load(f)

    # Hỗ trợ cả 2 format: {"selected_ids": [...]} hoặc {label: [ids]}
    if 'selected_ids' in selected_data:
        selected_ids = np.array(selected_data['selected_ids'])
    else:
        selected_ids = []
        for v in selected_data.values():
            selected_ids.extend(v)
        selected_ids = np.array(selected_ids)

    # Map selected_ids sang index trong train_sample_ids
    selected_indices = np.where(np.isin(train_sample_ids, selected_ids))[0]
    non_selected_indices = np.setdiff1d(np.arange(len(train_sample_ids)), selected_indices)

    # Load TF-IDF vectors và labels

    def load_sparse_csr(filename):
        loader = np.load(filename)
        return csr_matrix((loader['data'], loader['indices'], loader['indptr']), shape=loader['shape'])

    X_train_full_vec = load_sparse_csr('preprocessed_data/X_train_full_vec.npz')
    y_train_full = np.load('preprocessed_data/y_train_full.npy')

    # Lấy vector và label của cả hai nhóm
    X_selected = X_train_full_vec[selected_indices].toarray()
    y_selected = y_train_full[selected_indices]
    X_non_selected = X_train_full_vec[non_selected_indices].toarray()
    y_non_selected = y_train_full[non_selected_indices]

    # Kết hợp để fit PCA chung
    X_all = np.vstack([X_selected, X_non_selected])
    pca = PCA(n_components=2)
    X_all_pca = pca.fit_transform(X_all)
    X_selected_pca = X_all_pca[:len(X_selected)]
    X_non_selected_pca = X_all_pca[len(X_selected):]

    unique_labels = np.unique(y_train_full)
    n_classes = len(unique_labels)
    cols = int(ceil(sqrt(n_classes)))
    rows = int(ceil(n_classes / cols))

    fig, axes = plt.subplots(rows, cols, figsize=(4*cols, 4*rows), sharex=True, sharey=True)
    axes = axes.flatten()

    for idx, label in enumerate(unique_labels):
        ax = axes[idx]
        # Non-selected samples (gray)
        mask_non = (y_non_selected == label)
        ax.scatter(
            X_non_selected_pca[mask_non, 0], X_non_selected_pca[mask_non, 1],
            color='gray', alpha=0.3, label='Non-selected', s=18, edgecolors='none'
        )
        # Selected samples (red)
        mask_sel = (y_selected == label)
        ax.scatter(
            X_selected_pca[mask_sel, 0], X_selected_pca[mask_sel, 1],
            color='red', alpha=0.7, label='Cleanlab-selected', s=24, edgecolors='w'
        )
        ax.set_title(f'Class {label}')
        ax.set_xlabel('PC1')
        ax.set_ylabel('PC2')
        ax.grid(alpha=0.3)
        # Chỉ thêm legend ở subplot đầu tiên
        if idx == 0:
            ax.legend()

    # Ẩn subplot thừa nếu có
    for j in range(n_classes, len(axes)):
        fig.delaxes(axes[j])

    plt.suptitle(f'PCA: Cleanlab-selected (đỏ) vs. Non-selected (xám) of size {train_size}', fontsize=16)
    plt.tight_layout(rect=[0, 0, 1, 0.97])
    plt.show()

list_1 = [25, 60, 100, 200, 500, 1000]

import json
import numpy as np

log_file = f'Inter-classSeparation//interclass_results_kmedoids.txt'

results = []
with open(log_file, 'w') as f:
    f.write("=" * 80 + "\n\n")
    
    for train_size in list_1:
        f.write("=" * 80 + "\n")
        
        output_fname = f"Inter-classSeparation//selected_train_ids_{train_size}.json"
        !python Inter-classSeparation/select_method.py \
          --preprocessed_data_dir="preprocessed_data/" \
          --output_file={output_fname} \
          --train_size={train_size} \
          --random_seed=42 \

        with open(output_fname, 'r') as rf:
            selected_data = json.load(rf)
            selected_ids = np.array(selected_data['selected_ids'])
            
        # Get label counts
        labels = np.load('preprocessed_data/y_train_full.npy')[selected_ids]
        unique, counts = np.unique(labels, return_counts=True)
        
        # Write class distribution
        f.write(f"\nSelected training set created with {train_size} samples.\n")
        for label, count in zip(unique, counts):
            f.write(f"  Label {label}: {count} samples\n")
        
        # Run evaluation
        !python eval_medical.py \
          --preprocessed_data_dir preprocessed_data/ \
          --selected_ids_json Inter-classSeparation/selected_train_ids_{train_size}.json \
          --metrics_output_file Inter-classSeparation/results_selected_subset.json
        
        # Store and log evaluation results
        with open('Inter-classSeparation//results_selected_subset.json', 'r') as rf:
            metrics = json.load(rf)
        
        f.write("--- Evaluation Results ---\n")
        # Safely access metrics with fallback values
        f.write(f"Individual F1 Macro Scores: {metrics.get('f1_macro_scores', metrics.get('individual_f1_scores', []))}\n")
        f.write(f"Average F1 Macro Score: {metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0)):.4f}\n")
        f.write(f"Standard Deviation of F1 Macro Scores: {metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0)):.4f}\n")
        
        # Add blank lines for readability
        f.write("\n\n")
        f.flush()  # Force write to disk

print(f'\nSweep complete! Detailed log saved to {log_file}')

import numpy as np
import json
from scipy.sparse import csr_matrix
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from math import ceil, sqrt

# Load all sample IDs và Cleanlab selected IDs
train_sample_ids = np.load('preprocessed_data/train_sample_ids.npy')
for train_size in train_size_list:
    path = f'Inter-classSeparation/selected_train_ids_inter-class_{train_size}.json'
    with open(path, 'r') as f:
        selected_data = json.load(f)

    # Hỗ trợ cả 2 format: {"selected_ids": [...]} hoặc {label: [ids]}
    if 'selected_ids' in selected_data:
        selected_ids = np.array(selected_data['selected_ids'])
    else:
        selected_ids = []
        for v in selected_data.values():
            selected_ids.extend(v)
        selected_ids = np.array(selected_ids)

    # Map selected_ids sang index trong train_sample_ids
    selected_indices = np.where(np.isin(train_sample_ids, selected_ids))[0]
    non_selected_indices = np.setdiff1d(np.arange(len(train_sample_ids)), selected_indices)

    # Load TF-IDF vectors và labels

    def load_sparse_csr(filename):
        loader = np.load(filename)
        return csr_matrix((loader['data'], loader['indices'], loader['indptr']), shape=loader['shape'])

    X_train_full_vec = load_sparse_csr('preprocessed_data/X_train_full_vec.npz')
    y_train_full = np.load('preprocessed_data/y_train_full.npy')

    # Lấy vector và label của cả hai nhóm
    X_selected = X_train_full_vec[selected_indices].toarray()
    y_selected = y_train_full[selected_indices]
    X_non_selected = X_train_full_vec[non_selected_indices].toarray()
    y_non_selected = y_train_full[non_selected_indices]

    # Kết hợp để fit PCA chung
    X_all = np.vstack([X_selected, X_non_selected])
    pca = PCA(n_components=2)
    X_all_pca = pca.fit_transform(X_all)
    X_selected_pca = X_all_pca[:len(X_selected)]
    X_non_selected_pca = X_all_pca[len(X_selected):]

    unique_labels = np.unique(y_train_full)
    n_classes = len(unique_labels)
    cols = int(ceil(sqrt(n_classes)))
    rows = int(ceil(n_classes / cols))

    fig, axes = plt.subplots(rows, cols, figsize=(4*cols, 4*rows), sharex=True, sharey=True)
    axes = axes.flatten()

    for idx, label in enumerate(unique_labels):
        ax = axes[idx]
        # Non-selected samples (gray)
        mask_non = (y_non_selected == label)
        ax.scatter(
            X_non_selected_pca[mask_non, 0], X_non_selected_pca[mask_non, 1],
            color='gray', alpha=0.3, label='Non-selected', s=18, edgecolors='none'
        )
        # Selected samples (red)
        mask_sel = (y_selected == label)
        ax.scatter(
            X_selected_pca[mask_sel, 0], X_selected_pca[mask_sel, 1],
            color='red', alpha=0.7, label='Cleanlab-selected', s=24, edgecolors='w'
        )
        ax.set_title(f'Class {label}')
        ax.set_xlabel('PC1')
        ax.set_ylabel('PC2')
        ax.grid(alpha=0.3)
        # Chỉ thêm legend ở subplot đầu tiên
        if idx == 0:
            ax.legend()

    # Ẩn subplot thừa nếu có
    for j in range(n_classes, len(axes)):
        fig.delaxes(axes[j])

    plt.suptitle(f'PCA: Separation-selected (đỏ) vs. Non-selected (xám) of size {train_size}', fontsize=16)
    plt.tight_layout(rect=[0, 0, 1, 0.97])
    plt.show()

import numpy as np
import matplotlib.pyplot as plt

# Original data
train_sizes_orig = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_orig = [0.0999, 0.1000, 0.1792, 0.3471, 0.4952, 0.5189, 0.5087]
std_devs_orig = [0.0000, 0.0002, 0.0467, 0.0155, 0.0099, 0.0075, 0.0000]

# Cleanlab data (including full dataset)
train_sizes_cleanlab = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_cleanlab = [0.1569, 0.3716, 0.5255, 0.5683, 0.5887, 0.6046, 0.5087]
std_devs_cleanlab = [0.0924, 0.1302, 0.0210, 0.0046, 0.0010, 0.0011, 0.0000]

# Separation data
train_sizes_separation = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_separation = [0.0979, 0.3743, 0.5116, 0.5767, 0.6045, 0.6144, 0.5087]
std_devs_separation = [0.0616, 0.1002, 0.0165, 0.0071, 0.0012, 0.0006, 0.0000]

train_sizes_k = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_k = [0.1185, 0.2966, 0.4397, 0.4938, 0.5641, 0.5860, 0.5087]
std_devs_k = [0, 0, 0, 0, 0, 0, 0.0000]

plt.figure(figsize=(12, 8))

# Plot original data
plt.errorbar(train_sizes_orig, f1_scores_orig, yerr=std_devs_orig, 
             fmt='o-', capsize=5, label='Random Selection', color='blue')

# Plot cleanlab data
plt.errorbar(train_sizes_cleanlab, f1_scores_cleanlab, yerr=std_devs_cleanlab, 
             fmt='s-', capsize=5, label='Cleanlab Selection', color='red')

# Plot separation data (corrected this line)
plt.errorbar(train_sizes_separation, f1_scores_separation, yerr=std_devs_separation, 
             fmt='^-', capsize=5, label='Separation Selection', color='green')

plt.errorbar(train_sizes_k, f1_scores_k, yerr=std_devs_k, 
             fmt='^-', capsize=5, label='Kmedoids Selection', color='purple')

plt.xscale('log')
plt.grid(True, which="both", ls="-", alpha=0.2)

plt.xlabel('Training Set Size (log scale)')
plt.ylabel('F1 Macro Score')
plt.title('F1 Macro Score vs Training Size: Random vs Cleanlab vs Separation Selection')
plt.legend()


# Add value labels for each curve
#for x, y in zip(train_sizes_orig, f1_scores_orig):
#    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
#                xytext=(0,10), ha='center', color='blue')
#for x, y in zip(train_sizes_cleanlab, f1_scores_cleanlab):
#    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
#                xytext=(0,-15), ha='center', color='red')
#for x, y in zip(train_sizes_separation, f1_scores_separation):
#    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
#                xytext=(0,-25), ha='center', color='green')

plt.tight_layout()
plt.show()


