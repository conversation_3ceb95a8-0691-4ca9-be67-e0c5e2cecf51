# Add this as a new cell in your medical.ipynb notebook
# FINAL ANALYSIS: Cleanlab results vs true duplicates

import numpy as np
import pandas as pd
import scipy.sparse
from cleanlab.classification import CleanLearning
import sklearn.linear_model

def load_sparse_csr_matrix(file_path):
    """Load CSR sparse matrix from npz file"""
    loader = np.load(file_path)
    matrix = scipy.sparse.csr_matrix((loader['data'], loader['indices'], loader['indptr']), 
                                     shape=loader['shape'])
    loader.close()
    return matrix

print("="*80)
print("CLEANLAB ANALYSIS: Label Issues vs True Duplicates")
print("="*80)

# Load the data properly
print("Loading data...")
X_train_full_vec = load_sparse_csr_matrix("preprocessed_data/X_train_full_vec.npz")
y_train_full = np.load("preprocessed_data/y_train_full.npy")
train_sample_ids = np.load("preprocessed_data/train_sample_ids.npy")

print(f"✓ Loaded sparse matrix: {X_train_full_vec.shape[0]} samples, {X_train_full_vec.shape[1]} features")
print(f"✓ Matrix format: {type(X_train_full_vec)}")
print(f"✓ Classes: {np.unique(y_train_full)}")

# Load abstracts
try:
    with open('preprocessed_data/train_abstracts.txt', 'r', encoding='utf-8') as f:
        abstracts = [line.strip() for line in f.readlines()]
    print(f"✓ Loaded {len(abstracts)} abstracts")
except Exception as e:
    print(f"✗ Could not load abstracts: {e}")
    abstracts = None

print("\n" + "="*60)
print("ANALYSIS 1: Searching for TRUE DUPLICATES")
print("="*60)
print("True duplicates = identical feature vectors with different labels")

# Search for true duplicates
duplicate_pairs = []
feature_vector_map = {}

# Use a reasonable sample size for analysis
sample_size = min(2000, X_train_full_vec.shape[0])
print(f"Analyzing {sample_size} samples for true duplicates...")

for idx in range(sample_size):
    if idx % 500 == 0:
        print(f"  Processed {idx}/{sample_size} samples...")
    
    # Get feature vector as bytes for comparison
    feature_vector = X_train_full_vec[idx].toarray().tobytes()
    label = y_train_full[idx]
    
    if feature_vector in feature_vector_map:
        prev_idx, prev_label = feature_vector_map[feature_vector]
        if prev_label != label:
            # Found a true duplicate!
            duplicate_pairs.append({
                'idx1': prev_idx,
                'id1': train_sample_ids[prev_idx],
                'label1': prev_label,
                'idx2': idx,
                'id2': train_sample_ids[idx],
                'label2': label,
                'text1': abstracts[prev_idx] if abstracts else "Text not available",
                'text2': abstracts[idx] if abstracts else "Text not available"
            })
    else:
        feature_vector_map[feature_vector] = (idx, label)

print(f"\n🔍 RESULT: Found {len(duplicate_pairs)} true duplicate pairs")

if duplicate_pairs:
    print("\n📋 TRUE DUPLICATE PAIRS:")
    for i, pair in enumerate(duplicate_pairs[:3]):  # Show first 3
        print(f"\nPair {i+1}:")
        print(f"  Sample 1: ID {pair['id1']}, Label {pair['label1']}")
        print(f"  Sample 2: ID {pair['id2']}, Label {pair['label2']}")
        if abstracts:
            texts_identical = pair['text1'] == pair['text2']
            print(f"  Texts identical: {texts_identical}")
            print(f"  Text: {pair['text1'][:150]}...")
            if not texts_identical:
                print(f"  Text 2: {pair['text2'][:150]}...")
else:
    print("✅ No true duplicates found!")
    print("   This means no samples have identical feature vectors with different labels.")

print("\n" + "="*60)
print("ANALYSIS 2: What CLEANLAB actually identifies")
print("="*60)

# Run cleanlab on a subset
subset_size = min(1000, X_train_full_vec.shape[0])
X_subset = X_train_full_vec[:subset_size]
y_subset = y_train_full[:subset_size]

# Convert labels to 0-indexed for cleanlab
unique_y = np.unique(y_subset)
label_map = {old: new for new, old in enumerate(unique_y)}
y_zero_indexed = np.array([label_map[y] for y in y_subset])

print(f"Running cleanlab on {subset_size} samples...")
clf = sklearn.linear_model.LogisticRegression(solver='liblinear', random_state=42, max_iter=1000)
cl = CleanLearning(clf, seed=42, verbose=False, cv_n_folds=3)

issues = cl.find_label_issues(X_subset, y_zero_indexed)
cleanlab_issues = issues[issues['is_label_issue']]

print(f"\n🔍 RESULT: Cleanlab identified {len(cleanlab_issues)} label issues")

if len(cleanlab_issues) > 0:
    print(f"\n📋 CLEANLAB LABEL ISSUES (first 3):")
    for i, idx in enumerate(cleanlab_issues.index[:3]):
        label = y_subset[idx]
        sample_id = train_sample_ids[idx]
        quality_score = issues.loc[idx, 'label_quality']
        print(f"\nIssue {i+1}:")
        print(f"  Sample ID: {sample_id}, Label: {label}")
        print(f"  Quality Score: {quality_score:.3f}")
        if abstracts:
            print(f"  Text: {abstracts[idx][:150]}...")

print("\n" + "="*60)
print("ANALYSIS 3: Examining existing 'duplicate_issues.txt'")
print("="*60)

try:
    with open('Cleanlab/duplicate_issues.txt', 'r') as f:
        content = f.read()
    
    # Count lines that start with "Sample ID:"
    sample_lines = [line for line in content.split('\n') if line.startswith('Sample ID:')]
    print(f"📄 Found {len(sample_lines)} entries in duplicate_issues.txt")
    
    if sample_lines:
        print("\n📋 FIRST FEW ENTRIES FROM duplicate_issues.txt:")
        for i, line in enumerate(sample_lines[:3]):
            print(f"  {i+1}. {line}")
    
except FileNotFoundError:
    print("📄 File 'Cleanlab/duplicate_issues.txt' not found")

print("\n" + "="*60)
print("🎯 FINAL CONCLUSIONS")
print("="*60)

print(f"1. TRUE DUPLICATES FOUND: {len(duplicate_pairs)}")
if len(duplicate_pairs) == 0:
    print("   ✅ No samples have identical feature vectors with different labels")
    print("   ✅ This means the dataset doesn't have true duplicates")
else:
    print(f"   ⚠️  Found {len(duplicate_pairs)} pairs with identical features but different labels")

print(f"\n2. CLEANLAB LABEL ISSUES: {len(cleanlab_issues)}")
print("   📊 These are samples that appear mislabeled based on model predictions")
print("   📊 NOT the same as true duplicates")

print(f"\n3. WHAT'S IN 'duplicate_issues.txt':")
print("   📝 Contains cleanlab's label quality issues")
print("   📝 NOT true duplicates (identical texts with different labels)")
print("   📝 These are samples the model thinks might be mislabeled")

print(f"\n4. KEY INSIGHT:")
print("   🔑 Cleanlab identifies POTENTIALLY MISLABELED samples")
print("   🔑 This is different from finding IDENTICAL TEXTS with different labels")
print("   🔑 The current logic in cleanlab_selection.py was treating these as duplicates")

print(f"\n5. RECOMMENDATION:")
print("   💡 The 'duplicate_issues.txt' should be renamed to 'label_quality_issues.txt'")
print("   💡 True duplicate detection would need a different approach")
print("   💡 Consider text-based similarity instead of feature vector comparison")

if len(duplicate_pairs) > 0:
    print(f"\n📁 Saving {len(duplicate_pairs)} true duplicates to 'actual_duplicates_found.txt'")
    with open('actual_duplicates_found.txt', 'w') as f:
        f.write("ACTUAL DUPLICATE PAIRS FOUND\n")
        f.write("="*50 + "\n\n")
        for i, pair in enumerate(duplicate_pairs):
            f.write(f"Pair {i+1}:\n")
            f.write(f"  Sample 1: ID {pair['id1']}, Label {pair['label1']}\n")
            f.write(f"  Sample 2: ID {pair['id2']}, Label {pair['label2']}\n")
            f.write(f"  Texts identical: {pair['text1'] == pair['text2']}\n")
            f.write(f"  Text 1: {pair['text1']}\n")
            f.write(f"  Text 2: {pair['text2']}\n")
            f.write("\n" + "-"*50 + "\n\n")

print("\n✅ Analysis complete!")
print(f"📊 Analyzed {sample_size} samples for true duplicates")
print(f"📊 Ran cleanlab on {subset_size} samples")
print("🎯 Now you understand the difference between cleanlab issues and true duplicates!")
