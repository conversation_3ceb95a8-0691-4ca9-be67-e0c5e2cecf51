#!/usr/bin/env python3
"""
K-medoids selection on clean dataset (without cleanlab)
"""

import numpy as np
import scipy.sparse
from sklearn_extra.cluster import KMedoids
import json
import argparse
import time

def load_sparse_csr_matrix(file_path):
    """Load CSR sparse matrix from npz file"""
    loader = np.load(file_path)
    matrix = scipy.sparse.csr_matrix((loader['data'], loader['indices'], loader['indptr']), 
                                     shape=loader['shape'])
    loader.close()
    return matrix

def balanced_kmedoids_selection(X, labels, sample_ids, target_size, class_balance='equal', random_seed=42):
    """Select a balanced subset using K-medoids clustering."""
    print(f"🎯 Running balanced K-medoids selection for {target_size} samples...")
    
    unique_classes = np.unique(labels)
    num_classes = len(unique_classes)
    
    print(f"📊 Classes: {unique_classes}")
    print(f"📊 Original class distribution:")
    for cls in unique_classes:
        count = np.sum(labels == cls)
        print(f"  Class {cls}: {count} samples")
    
    # Determine per-class target sizes
    if class_balance == 'equal':
        per_class_size = target_size // num_classes
        class_sizes = {cls: per_class_size for cls in unique_classes}
        # Distribute remaining samples
        remaining = target_size - (per_class_size * num_classes)
        for i in range(remaining):
            cls = unique_classes[i % num_classes]
            class_sizes[cls] += 1
    else:  # proportional
        class_counts = {cls: np.sum(labels == cls) for cls in unique_classes}
        total_samples = len(labels)
        class_sizes = {
            cls: max(1, int(target_size * (count / total_samples)))
            for cls, count in class_counts.items()
        }
        # Adjust to ensure exact target size
        current_total = sum(class_sizes.values())
        if current_total != target_size:
            diff = target_size - current_total
            sorted_classes = sorted(class_counts.items(), key=lambda x: x[1], reverse=True)
            for i in range(abs(diff)):
                cls = sorted_classes[i % len(sorted_classes)][0]
                class_sizes[cls] += 1 if diff > 0 else -1
    
    print(f"🎯 Target class distribution:")
    for cls in unique_classes:
        print(f"  Class {cls}: {class_sizes[cls]} samples")
    
    # Select samples for each class using K-medoids
    selected_indices = []
    
    for cls in unique_classes:
        target_count = class_sizes[cls]
        if target_count <= 0:
            continue
            
        class_indices = np.where(labels == cls)[0]
        available_count = len(class_indices)
        
        print(f"⚙️  Selecting {target_count} from {available_count} samples of class {cls}...")
        
        if target_count >= available_count:
            selected_indices.extend(class_indices)
        else:
            # Use K-medoids
            X_class = X[class_indices]
            
            if scipy.sparse.issparse(X_class):
                X_class_dense = X_class.toarray()
            else:
                X_class_dense = X_class
            
            kmedoids = KMedoids(n_clusters=target_count, init="k-medoids++", random_state=random_seed)
            kmedoids.fit(X_class_dense)
            
            selected_class_indices = class_indices[kmedoids.medoid_indices_]
            selected_indices.extend(selected_class_indices)
    
    selected_indices = np.array(selected_indices)
    selected_sample_ids = sample_ids[selected_indices]
    
    print(f"✅ Selected {len(selected_indices)} samples total")
    print("📊 Final class distribution:")
    selected_labels = labels[selected_indices]
    for cls in unique_classes:
        count = np.sum(selected_labels == cls)
        print(f"  Class {cls}: {count} samples")
    
    return selected_indices, selected_sample_ids

def main(
    preprocessed_data_dir: str = "preprocessed_data_clean/",
    output_file: str = "CleanKmedoids/selected_train_ids_clean_kmedoids.json",
    train_size: int = 1000,
    random_seed: int = 42,
    class_balance: str = 'equal'
):
    """
    Select training subset using K-medoids on clean dataset
    
    Args:
        preprocessed_data_dir: Directory containing preprocessed clean data
        output_file: Output JSON file for selected IDs
        train_size: Target training set size
        random_seed: Random seed for reproducibility
        class_balance: 'equal' or 'proportional' class balancing
    """
    
    print("="*80)
    print("🎯 K-MEDOIDS SELECTION ON CLEAN DATASET")
    print("="*80)
    
    start_load_time = time.time()
    
    # Load data
    print(f"📂 Loading data from {preprocessed_data_dir}...")
    X_train_full_vec = load_sparse_csr_matrix(f"{preprocessed_data_dir}/X_train_full_vec.npz")
    y_train_full = np.load(f"{preprocessed_data_dir}/y_train_full.npy")
    train_sample_ids = np.load(f"{preprocessed_data_dir}/train_sample_ids.npy")
    
    print(f"✅ Data loaded in {time.time() - start_load_time:.2f} seconds")
    print(f"📊 Shape: {X_train_full_vec.shape}")
    print(f"📊 Classes: {np.unique(y_train_full)}")
    
    # Run K-medoids selection
    print(f"\n🎯 Selecting {train_size} samples using K-medoids with {class_balance} class balance...")
    start_select_time = time.time()
    
    selected_indices, selected_ids = balanced_kmedoids_selection(
        X_train_full_vec, y_train_full, train_sample_ids, 
        train_size, class_balance, random_seed
    )
    
    print(f"✅ Selection completed in {time.time() - start_select_time:.2f} seconds")
    
    # Save results
    results = {
        'selected_ids': selected_ids.tolist(),
        'method': 'clean_kmedoids',
        'parameters': {
            'train_size': train_size,
            'class_balance': class_balance,
            'random_seed': random_seed,
            'preprocessed_data_dir': preprocessed_data_dir
        },
        'selection_info': {
            'total_available': len(y_train_full),
            'selected_count': len(selected_ids),
            'class_distribution': {
                int(cls): int(np.sum(y_train_full[selected_indices] == cls))
                for cls in np.unique(y_train_full)
            }
        }
    }
    
    # Create output directory if needed
    import os
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to {output_file}")
    print("="*80)
    print("📋 SUMMARY")
    print("="*80)
    print(f"📊 Available samples: {len(y_train_full)}")
    print(f"🎯 Selected samples: {len(selected_ids)}")
    print(f"⚙️  Selection method: K-medoids with {class_balance} class balance")
    print(f"🎲 Random seed: {random_seed}")
    print(f"📁 Output: {output_file}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='K-medoids selection on clean dataset')
    parser.add_argument('--preprocessed_data_dir', default='preprocessed_data_clean/', help='Directory with preprocessed data')
    parser.add_argument('--output_file', default='CleanKmedoids/selected_train_ids_clean_kmedoids.json', help='Output JSON file')
    parser.add_argument('--train_size', type=int, default=1000, help='Target training set size')
    parser.add_argument('--random_seed', type=int, default=42, help='Random seed')
    parser.add_argument('--class_balance', choices=['equal', 'proportional'], default='equal', help='Class balancing strategy')

    args = parser.parse_args()

    main(
        preprocessed_data_dir=args.preprocessed_data_dir,
        output_file=args.output_file,
        train_size=args.train_size,
        random_seed=args.random_seed,
        class_balance=args.class_balance
    )
