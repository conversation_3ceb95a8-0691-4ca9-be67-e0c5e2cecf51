#!/usr/bin/env python3
"""
Clean dataset by removing duplicates, then preprocess using the same format as preprocess_medical.py
"""

import pandas as pd
import numpy as np
import sklearn.feature_extraction.text
import tqdm
import os
import sys
import string
import re
import nltk
from collections import Counter
import joblib
import fire
import json

def clean_text(text):
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = re.sub(r'\@\w+|\#', '', text)
    text = re.sub(r'[%s]' % re.escape(string.punctuation), '', text)
    text = re.sub(r'\d+', '', text)
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def can_create_dir(path):
    try:
        test_dir = os.path.join(path, '__test_write__')
        os.makedirs(test_dir, exist_ok=True)
        os.rmdir(test_dir)
        return True
    except Exception:
        return False

def setup_nltk_data(download_dir=None):
    if download_dir is None:
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            download_dir = os.path.join(script_dir, "nltk_data")
        except NameError: 
             download_dir = os.path.join(os.getcwd(), "nltk_data")

        if hasattr(sys, 'prefix') and sys.prefix != sys.base_prefix:
             venv_nltk_dir = os.path.join(sys.prefix, 'nltk_data')
             if os.path.exists(venv_nltk_dir) or can_create_dir(venv_nltk_dir):
                 download_dir = venv_nltk_dir

    try:
        os.makedirs(download_dir, exist_ok=True)
    except OSError as e:
        print(f"Error creating NLTK download directory {download_dir}: {e}.")
        return 

    if download_dir not in nltk.data.path:
         nltk.data.path.insert(0, download_dir) 

    required_resources = ['punkt', 'stopwords']
    for resource in required_resources:
        try:
            nltk.data.find(f'tokenizers/{resource}' if resource == 'punkt' else f'corpora/{resource}')
        except LookupError:
             try:
                 nltk.download(resource, download_dir=download_dir, quiet=True)
                 print(f"-> '{resource}' download attempted.")
             except Exception as e:
                 print(f"-> Error during NLTK '{resource}' download to {download_dir}: {e}. Check directory permissions.")

try:
    import nltk
    setup_nltk_data()
    from nltk.corpus import stopwords
    stop_words = set(stopwords.words('english'))
    print("NLTK data (punkt, stopwords) is ready.")
except ImportError:
    print("NLTK not installed. Skipping NLTK-based cleaning.")
    stop_words = set()
except LookupError:
     print("NLTK data not fully set up. Stop words might be incomplete.")
     stop_words = set()

def find_and_remove_duplicates(df, text_column='medical_abstract', label_column='condition_label'):
    """Find and remove samples with identical text but different labels."""
    print("🔍 Finding duplicates (identical text with different labels)...")
    
    # Group by text to find duplicates
    text_groups = df.groupby(text_column)
    
    duplicate_info = []
    indices_to_remove = set()
    
    for text, group in text_groups:
        if len(group) > 1:  # Multiple samples with same text
            unique_labels = group[label_column].unique()
            if len(unique_labels) > 1:  # Different labels for same text
                duplicate_info.append({
                    'text': text[:200] + "..." if len(text) > 200 else text,
                    'labels': list(unique_labels),
                    'sample_ids': list(group.index),
                    'count': len(group)
                })
                # Remove all instances of this duplicate text
                indices_to_remove.update(group.index)
    
    print(f"📊 Found {len(duplicate_info)} duplicate text groups with different labels")
    print(f"🗑️  Removing {len(indices_to_remove)} samples total")
    
    if duplicate_info:
        print("\n📋 Duplicate examples:")
        for i, dup in enumerate(duplicate_info[:3]):
            print(f"  {i+1}. Text: {dup['text']}")
            print(f"     Labels: {dup['labels']}")
            print(f"     Count: {dup['count']}")
    
    # Create cleaned dataframe
    cleaned_df = df.drop(indices_to_remove).reset_index(drop=True)
    
    print(f"\n📈 Dataset size: {len(df)} -> {len(cleaned_df)} (removed {len(df) - len(cleaned_df)} samples)")
    
    return cleaned_df, duplicate_info

def main(
    input_dir: str = "",
    output_dir: str = "preprocessed_data_clean/",
    train_file: str = "medical_train.csv",
    test_file: str = "medical_test.csv",
    max_features: int = 10000 
):
    """
    Clean dataset and preprocess using the same format as preprocess_medical.py
    
    Args:
        input_dir: Directory containing the raw CSV files.
        output_dir: Directory to save the preprocessed .npz and .npy files.
        train_file: Name of the training CSV file in input_dir.
        test_file: Name of the testing CSV file in input_dir.
        max_features: Maximum number of features for TfidfVectorizer.
    """
    
    print("="*80)
    print("🧹 CLEAN DATASET + PREPROCESS")
    print("="*80)
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    train_csv_path = os.path.join(input_dir, train_file)
    test_csv_path = os.path.join(input_dir, test_file)

    # Load data
    print(f"📂 Loading training data from {train_csv_path}...")
    train_df = pd.read_csv(train_csv_path)
    print(f"📂 Loading test data from {test_csv_path}...")
    test_df = pd.read_csv(test_csv_path)
    
    print(f"✅ Loaded {len(train_df)} training samples, {len(test_df)} test samples")
    
    # Find and remove duplicates from training data
    cleaned_train_df, duplicate_info = find_and_remove_duplicates(
        train_df, 'medical_abstract', 'condition_label'
    )
    
    # Save duplicate info
    duplicate_info_file = os.path.join(output_dir, 'duplicates_removed.json')
    with open(duplicate_info_file, 'w') as f:
        json.dump(duplicate_info, f, indent=2)
    print(f"💾 Saved duplicate info to {duplicate_info_file}")
    
    # Save original abstracts to text file (from cleaned data)
    with open(os.path.join(output_dir, 'train_abstracts.txt'), 'w', encoding='utf-8') as f:
        for abstract in cleaned_train_df["medical_abstract"].values:
            f.write(str(abstract) + '\n')
    
    # Get data arrays
    train_sample_ids = cleaned_train_df.index.values 
    X_train_full = cleaned_train_df["medical_abstract"].values
    y_train_full = cleaned_train_df["condition_label"].values
    X_test = test_df["medical_abstract"].values
    y_test = test_df["condition_label"].values

    # Clean text
    print("🔧 Cleaning text data...")
    X_train_full_cleaned = [clean_text(text) for text in tqdm.tqdm(X_train_full, desc="Cleaning Train Data")]
    X_test_cleaned = [clean_text(text) for text in tqdm.tqdm(X_test, desc="Cleaning Test Data")]

    # TF-IDF vectorization
    print("\n🔧 Fitting TF-IDF vectorizer...")
    vectorizer = sklearn.feature_extraction.text.TfidfVectorizer(
        max_features=max_features,
        stop_words=list(stop_words) if stop_words else 'english',
    )

    X_train_full_vec = vectorizer.fit_transform(X_train_full_cleaned)
    X_test_vec = vectorizer.transform(X_test_cleaned)

    print(f"✅ TF-IDF vectorization complete. Training data shape: {X_train_full_vec.shape}, Test data shape: {X_test_vec.shape}")

    # Save data in the same format as original preprocessing
    print("💾 Saving preprocessed data...")
    np.savez_compressed(os.path.join(output_dir, 'X_train_full_vec.npz'), 
                       data=X_train_full_vec.data, 
                       indices=X_train_full_vec.indices, 
                       indptr=X_train_full_vec.indptr, 
                       shape=X_train_full_vec.shape)
    np.save(os.path.join(output_dir, 'y_train_full.npy'), y_train_full)
    np.save(os.path.join(output_dir, 'train_sample_ids.npy'), train_sample_ids)

    np.savez_compressed(os.path.join(output_dir, 'X_test_vec.npz'), 
                       data=X_test_vec.data, 
                       indices=X_test_vec.indices, 
                       indptr=X_test_vec.indptr, 
                       shape=X_test_vec.shape)
    np.save(os.path.join(output_dir, 'y_test.npy'), y_test)

    # Save vectorizer
    joblib.dump(vectorizer, os.path.join(output_dir, 'tfidf_vectorizer.joblib'))

    print("✅ Preprocessed data saved successfully.")
    print(f"📁 Saved to: {output_dir}")
    
    # Summary
    print("\n" + "="*60)
    print("📋 SUMMARY")
    print("="*60)
    print(f"📊 Original training dataset: {len(train_df)} samples")
    print(f"🗑️  Duplicates removed: {len(duplicate_info)} groups ({len(train_df) - len(cleaned_train_df)} samples)")
    print(f"🧹 Cleaned training dataset: {len(cleaned_train_df)} samples")
    print(f"📊 Test dataset: {len(test_df)} samples (unchanged)")
    print(f"🔧 TF-IDF features: {X_train_full_vec.shape[1]}")
    print(f"📁 Output directory: {output_dir}")

if __name__ == "__main__":
    fire.Fire(main)
