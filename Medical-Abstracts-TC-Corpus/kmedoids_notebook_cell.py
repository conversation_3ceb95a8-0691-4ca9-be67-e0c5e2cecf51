# Add this as a new cell in your Jupyter notebook
# K-MEDOIDS SAMPLE SELECTION WITH DUPLICATE REMOVAL (Notebook Version)

import json

# Configuration - replace argparse with direct variable assignments
class Args:
    def __init__(self):
        self.preprocessed_data_dir = 'preprocessed_data/'  # Change this to your data directory
        self.output_file = 'selected_train_ids_kmedoids.json'
        self.train_size = 1000  # Change this to your desired size
        self.random_seed = 42
        self.class_balance = 'equal'  # or 'proportional'
        self.extra_frac = 0.5

args = Args()

print("="*80)
print("K-MEDOIDS SAMPLE SELECTION WITH DUPLICATE REMOVAL")
print("="*80)
print(f"Preprocessed data directory: {args.preprocessed_data_dir}")
print(f"Output file: {args.output_file}")
print(f"Target train size: {args.train_size}")
print(f"Random seed: {args.random_seed}")
print(f"Class balance: {args.class_balance}")
print(f"Extra fraction: {args.extra_frac}")
print("="*80)

# Initialize selector
selector = KMedoidsSelector(random_seed=args.random_seed)

# Load data
X_train, y_train, texts = selector.load_data(args.preprocessed_data_dir)

# Remove conflicting duplicates
X_clean, y_clean, clean_indices = selector.remove_duplicates(X_train, y_train, texts)

# Select samples using K-Medoids
selected_indices = selector.select_samples_kmedoids(
    X_clean, y_clean, clean_indices, 
    args.train_size, args.class_balance, args.extra_frac
)

# Save results
result = {
    'selected_ids': selected_indices.tolist(),
    'total_selected': len(selected_indices),
    'method': 'kmedoids_with_duplicate_removal',
    'parameters': {
        'train_size': args.train_size,
        'random_seed': args.random_seed,
        'class_balance': args.class_balance,
        'extra_frac': args.extra_frac
    },
    'duplicate_info': {
        'conflicting_groups': len(selector.duplicate_remover.duplicate_groups) if selector.duplicate_remover.duplicate_groups else 0,
        'conflicting_samples_removed': len(selector.duplicate_remover.conflicting_indices)
    }
}

with open(args.output_file, 'w') as f:
    json.dump(result, f, indent=2)

print(f"\nResults saved to {args.output_file}")
print("="*80)
