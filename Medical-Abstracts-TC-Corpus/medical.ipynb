{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fcaa6df5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Medical TF-IDF Nested Cross-Validation Selection Script ---\n", "Loading preprocessed training data for selection...\n", "Full Training data shape: (11550, 10000)\n", "Full training data contains 11550 samples across 5 classes.\n", "\n", "--- Starting Medical TF-IDF Nested Cross-Validation Selection Baseline ---\n", "Train set size limit (final selection): 25\n", "Random seed: 42\n", "Nested CV folds: 10\n", "Outer split train proportion: 0.8\n", "\n", "Running Nested Cross-Validation:\n", "Outer CV Fold:   0%|                                     | 0/10 [00:00<?, ?it/s]^C\n", "Outer CV Fold:   0%|                                     | 0/10 [00:03<?, ?it/s]\n", "Traceback (most recent call last):\n", "  File \"/drive1/nammt/dataperf-speech-example/Medical-Abstracts-TC-Corpus-new/Baseline/select_medical.py\", line 279, in <module>\n", "    fire.Fire(main)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/fire/core.py\", line 135, in Fire\n", "    component_trace = _Fire(component, args, parsed_flag_args, context, name)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/fire/core.py\", line 468, in _Fire\n", "    component, remaining_args = _CallAndUpdateTrace(\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/fire/core.py\", line 684, in _CallAndUpdateTrace\n", "    component = fn(*varargs, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/Medical-Abstracts-TC-Corpus-new/Baseline/select_medical.py\", line 267, in main\n", "    selected_set = selector.select()\n", "  File \"/drive1/nammt/dataperf-speech-example/Medical-Abstracts-TC-Corpus-new/Baseline/select_medical.py\", line 175, in select\n", "    pred_y = clf.predict(X_inner_val)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/ensemble/_voting.py\", line 436, in predict\n", "    maj = np.argmax(self.predict_proba(X), axis=1)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/ensemble/_voting.py\", line 477, in predict_proba\n", "    self._collect_probas(X), axis=0, weights=self._weights_not_none\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/ensemble/_voting.py\", line 452, in _collect_probas\n", "    return np.asarray([clf.predict_proba(X) for clf in self.estimators_])\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/ensemble/_voting.py\", line 452, in <listcomp>\n", "    return np.asarray([clf.predict_proba(X) for clf in self.estimators_])\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/svm/_base.py\", line 873, in predict_proba\n", "    return pred_proba(X)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/svm/_base.py\", line 941, in _sparse_predict_proba\n", "    return libsvm_sparse.libsvm_sparse_predict_proba(\n", "KeyboardInterrupt\n"]}], "source": ["TRAIN_SIZE = 25\n", "!python Baseline/select_medical.py \\\n", "  --preprocessed_data_dir=\"preprocessed_data/\" \\\n", "  --output_file=\"Baseline/selected_train_ids.json\" \\\n", "  --train_size={TRAIN_SIZE} \\\n", "  --random_seed=42 \\\n", "  --n_folds=10 \\\n", "  --outer_split_size=0.8"]}, {"cell_type": "code", "execution_count": null, "id": "593621ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Medical Abstract Evaluation Script ---\n", "Loading preprocessed data...\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "\n", "--- Loading Selected Training IDs from selected_train_ids.json ---\n", "Filtered training data using selected subset. Shape: (25, 10000)\n", "\n", "--- Training and Evaluating with Multiple Random Seeds ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:06<00:00,  1.61it/s]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463]\n", "Average F1 Macro Score: 0.0999\n", "Standard Deviation of F1 Macro Scores: 0.0000\n", "Metrics saved to results_selected_subset.json\n"]}], "source": ["!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Baseline/selected_train_ids.json --metrics_output_file Baseline/results_selected_subset.json"]}, {"cell_type": "code", "execution_count": null, "id": "a4cb8348", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "0a3b4521", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "00c00682", "metadata": {}, "outputs": [{"data": {"text/plain": ["11550"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["train_ids_path = os.path.join('preprocessed_data', 'train_sample_ids.npy')\n", "train_sample_ids = np.load(train_ids_path)\n", "max_train_size = len(train_sample_ids)\n", "\n", "max_train_size"]}, {"cell_type": "markdown", "id": "f4614428", "metadata": {}, "source": ["## Training Size Sweep Strategy\n", "The previous approach used only 10 log-spaced points between 10 and 11550, which is too sparse to reliably detect the elbow point in the learning curve. \n", "\n", "To improve this, we:\n", "- Increase the number of log-spaced points (e.g., 20 or 30).\n", "- Add more points at the lower end (e.g., 25, 30, 40, 50, 60, 100, 200, 500) to capture the rapid change in performance.\n", "- Always include the full training set size.\n", "\n", "This gives a much denser and more informative curve."]}, {"cell_type": "code", "execution_count": 20, "id": "360deea1", "metadata": {}, "outputs": [], "source": ["fixed_small_sizes = [25, 60, 100, 200, 500, 1000]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "b9f4179d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running for train_size=25\n", "--- Medical TF-IDF Nested Cross-Validation Selection Script ---\n", "Loading preprocessed training data for selection...\n", "--- Medical TF-IDF Nested Cross-Validation Selection Script ---\n", "Loading preprocessed training data for selection...\n", "Preprocessed data loaded. Full Training data shape: (11550, 10000)\n", "Original Sample IDs shape: (11550,)\n", "Full training data contains 11550 samples across 5 classes.\n", "\n", "--- Starting Medical TF-IDF Nested Cross-Validation Selection Baseline ---\n", "Train set size limit (final selection): 25\n", "Random seed: 42\n", "Nester CV folds: 10\n", "Outer split train proportion: 0.8\n", "\n", "Running Nested Cross-Validation:\n", "Outer CV Fold:   0%|                                     | 0/10 [00:00<?, ?it/s]Preprocessed data loaded. Full Training data shape: (11550, 10000)\n", "Original Sample IDs shape: (11550,)\n", "Full training data contains 11550 samples across 5 classes.\n", "\n", "--- Starting Medical TF-IDF Nested Cross-Validation Selection Baseline ---\n", "Train set size limit (final selection): 25\n", "Random seed: 42\n", "Nester CV folds: 10\n", "Outer split train proportion: 0.8\n", "\n", "Running Nested Cross-Validation:\n", "Outer CV Fold:  60%|█████████████████▍           | 6/10 [00:48<00:32,  8.12s/it]\n", "Traceback (most recent call last):\n", "  File \"/drive1/nammt/dataperf-speech-example/Medical-Abstracts-TC-Corpus/select_medical.py\", line 307, in <module>\n", "    fire.Fire(main)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/fire/core.py\", line 135, in Fire\n", "    component_trace = _Fire(component, args, parsed_flag_args, context, name)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/fire/core.py\", line 468, in _Fire\n", "    component, remaining_args = _CallAndUpdateTrace(\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/fire/core.py\", line 684, in _CallAndUpdateTrace\n", "    component = fn(*varargs, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/Medical-Abstracts-TC-Corpus/select_medical.py\", line 294, in main\n", "    selected_set = selector.select()\n", "  File \"/drive1/nammt/dataperf-speech-example/Medical-Abstracts-TC-Corpus/select_medical.py\", line 192, in select\n", "    clf.fit(X_inner_train, y_inner_train)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/base.py\", line 1389, in wrapper\n", "    return fit_method(estimator, *args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/validation.py\", line 63, in inner_f\n", "    return f(*args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/ensemble/_voting.py\", line 419, in fit\n", "    return super().fit(X, transformed_y, **fit_params)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/ensemble/_voting.py\", line 100, in fit\n", "    self.estimators_ = Parallel(n_jobs=self.n_jobs)(\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/parallel.py\", line 77, in __call__\n", "    return super().__call__(iterable_with_config)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/joblib/parallel.py\", line 1918, in __call__\n", "    return output if self.return_generator else list(output)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/joblib/parallel.py\", line 1847, in _get_sequential_output\n", "    res = func(*args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/parallel.py\", line 139, in __call__\n", "    return self.function(*args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/ensemble/_base.py\", line 39, in _fit_single_estimator\n", "    estimator.fit(X, y, **fit_params)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/base.py\", line 1389, in wrapper\n", "    return fit_method(estimator, *args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/linear_model/_logistic.py\", line 1350, in fit\n", "    fold_coefs_ = Parallel(n_jobs=self.n_jobs, verbose=self.verbose, prefer=prefer)(\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/parallel.py\", line 77, in __call__\n", "    return super().__call__(iterable_with_config)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/joblib/parallel.py\", line 1918, in __call__\n", "    return output if self.return_generator else list(output)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/joblib/parallel.py\", line 1847, in _get_sequential_output\n", "    res = func(*args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/parallel.py\", line 139, in __call__\n", "    return self.function(*args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/linear_model/_logistic.py\", line 451, in _logistic_regression_path\n", "    opt_res = optimize.minimize(\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/sit^C\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_lbfgsb_py.py\", line 441, in _minimize_lbfgsb\n", "    f, g = func_and_grad(x)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_differentiable_functions.py\", line 344, in fun_and_grad\n", "    self._update_fun()\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_differentiable_functions.py\", line 295, in _update_fun\n", "    fx = self._wrapped_fun(self.x)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_differentiable_functions.py\", line 21, in wrapped\n", "    fx = fun(np.copy(x), *args)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_optimize.py\", line 80, in __call__\n", "    self._compute_if_needed(x, *args)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_optimize.py\", line 74, in _compute_if_needed\n", "    fg = self.fun(x, *args)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/linear_model/_linear_loss.py\", line 324, in loss_gradient\n", "    loss += self.l2_penalty(weights, l2_reg_strength)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/linear_model/_linear_loss.py\", line 209, in l2_penalty\n", "    norm2_w = weights @ weights if weights.ndim == 1 else squared_norm(weights)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/extmath.py\", line 44, in squared_norm\n", "    return np.dot(x, x)\n", "KeyboardInterrupt\n", "Outer CV Fold:  60%|█████████████████▍           | 6/10 [00:48<00:32,  8.12s/it]\n", "Traceback (most recent call last):\n", "  File \"/drive1/nammt/dataperf-speech-example/Medical-Abstracts-TC-Corpus/select_medical.py\", line 307, in <module>\n", "    fire.Fire(main)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/fire/core.py\", line 135, in Fire\n", "    component_trace = _Fire(component, args, parsed_flag_args, context, name)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/fire/core.py\", line 468, in _Fire\n", "    component, remaining_args = _CallAndUpdateTrace(\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/fire/core.py\", line 684, in _CallAndUpdateTrace\n", "    component = fn(*varargs, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/Medical-Abstracts-TC-Corpus/select_medical.py\", line 294, in main\n", "    selected_set = selector.select()\n", "  File \"/drive1/nammt/dataperf-speech-example/Medical-Abstracts-TC-Corpus/select_medical.py\", line 192, in select\n", "    clf.fit(X_inner_train, y_inner_train)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/base.py\", line 1389, in wrapper\n", "    return fit_method(estimator, *args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/validation.py\", line 63, in inner_f\n", "    return f(*args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/ensemble/_voting.py\", line 419, in fit\n", "    return super().fit(X, transformed_y, **fit_params)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/ensemble/_voting.py\", line 100, in fit\n", "    self.estimators_ = Parallel(n_jobs=self.n_jobs)(\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/parallel.py\", line 77, in __call__\n", "    return super().__call__(iterable_with_config)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/joblib/parallel.py\", line 1918, in __call__\n", "    return output if self.return_generator else list(output)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/joblib/parallel.py\", line 1847, in _get_sequential_output\n", "    res = func(*args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/parallel.py\", line 139, in __call__\n", "    return self.function(*args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/ensemble/_base.py\", line 39, in _fit_single_estimator\n", "    estimator.fit(X, y, **fit_params)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/base.py\", line 1389, in wrapper\n", "    return fit_method(estimator, *args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/linear_model/_logistic.py\", line 1350, in fit\n", "    fold_coefs_ = Parallel(n_jobs=self.n_jobs, verbose=self.verbose, prefer=prefer)(\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/parallel.py\", line 77, in __call__\n", "    return super().__call__(iterable_with_config)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/joblib/parallel.py\", line 1918, in __call__\n", "    return output if self.return_generator else list(output)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/joblib/parallel.py\", line 1847, in _get_sequential_output\n", "    res = func(*args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/parallel.py\", line 139, in __call__\n", "    return self.function(*args, **kwargs)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/linear_model/_logistic.py\", line 451, in _logistic_regression_path\n", "    opt_res = optimize.minimize(\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/sit^C\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_lbfgsb_py.py\", line 441, in _minimize_lbfgsb\n", "    f, g = func_and_grad(x)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_differentiable_functions.py\", line 344, in fun_and_grad\n", "    self._update_fun()\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_differentiable_functions.py\", line 295, in _update_fun\n", "    fx = self._wrapped_fun(self.x)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_differentiable_functions.py\", line 21, in wrapped\n", "    fx = fun(np.copy(x), *args)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_optimize.py\", line 80, in __call__\n", "    self._compute_if_needed(x, *args)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/scipy/optimize/_optimize.py\", line 74, in _compute_if_needed\n", "    fg = self.fun(x, *args)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/linear_model/_linear_loss.py\", line 324, in loss_gradient\n", "    loss += self.l2_penalty(weights, l2_reg_strength)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/linear_model/_linear_loss.py\", line 209, in l2_penalty\n", "    norm2_w = weights @ weights if weights.ndim == 1 else squared_norm(weights)\n", "  File \"/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/sklearn/utils/extmath.py\", line 44, in squared_norm\n", "    return np.dot(x, x)\n", "KeyboardInterrupt\n", "--- Medical Abstract Evaluation Script ---\n", "Loading preprocessed data...\n", "--- Medical Abstract Evaluation Script ---\n", "Loading preprocessed data...\n", "Preprocessed data loaded.\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "Warning: Config file config_eval.yaml not found. Using default settings.\n", "\n", "--- Loading Selected Training IDs from selected_train_ids.json ---\n", "Filtered training data using selected subset. Shape: (25, 10000)\n", "\n", "--- Starting Model Training (on selected subset) and Evaluation (on full test) ---\n", "Evaluating Seeds:   0%|                                  | 0/10 [00:00<?, ?it/s]Preprocessed data loaded.\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "Warning: Config file config_eval.yaml not found. Using default settings.\n", "\n", "--- Loading Selected Training IDs from selected_train_ids.json ---\n", "Filtered training data using selected subset. Shape: (25, 10000)\n", "\n", "--- Starting Model Training (on selected subset) and Evaluation (on full test) ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:05<00:00,  1.92it/s]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463]\n", "Average F1 Macro Score: 0.0999\n", "Standard Deviation of F1 Macro Scores: 0.0000\n", "Metrics saved to results_selected_subset.json\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:05<00:00,  1.92it/s]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463]\n", "Average F1 Macro Score: 0.0999\n", "Standard Deviation of F1 Macro Scores: 0.0000\n", "Metrics saved to results_selected_subset.json\n", "Running for train_size=60\n", "Running for train_size=60\n", "--- Medical TF-IDF Nested Cross-Validation Selection Script ---\n", "Loading preprocessed training data for selection...\n", "--- Medical TF-IDF Nested Cross-Validation Selection Script ---\n", "Loading preprocessed training data for selection...\n", "Preprocessed data loaded. Full Training data shape: (11550, 10000)\n", "Original Sample IDs shape: (11550,)\n", "Full training data contains 11550 samples across 5 classes.\n", "\n", "--- Starting Medical TF-IDF Nested Cross-Validation Selection Baseline ---\n", "Train set size limit (final selection): 60\n", "Random seed: 42\n", "Nester CV folds: 10\n", "Outer split train proportion: 0.8\n", "\n", "Running Nested Cross-Validation:\n", "Outer CV Fold:   0%|                                     | 0/10 [00:00<?, ?it/s]Preprocessed data loaded. Full Training data shape: (11550, 10000)\n", "Original Sample IDs shape: (11550,)\n", "Full training data contains 11550 samples across 5 classes.\n", "\n", "--- Starting Medical TF-IDF Nested Cross-Validation Selection Baseline ---\n", "Train set size limit (final selection): 60\n", "Random seed: 42\n", "Nester CV folds: 10\n", "Outer split train proportion: 0.8\n", "\n", "Running Nested Cross-Validation:\n", "Outer CV Fold: 100%|████████████████████████████| 10/10 [02:38<00:00, 15.88s/it]\n", "\n", "Nested CV Selection complete. Best F1 Macro Score found during CV: 0.2214\n", "Selected 60 sample indices based on nested CV performance.\n", "Selected training set created with 60 samples.\n", "  Label 1: 13 samples\n", "  Label 2: 6 samples\n", "  Label 3: 8 samples\n", "  Label 4: 13 samples\n", "  Label 5: 20 samples\n", "\n", "Selection script finished. Selected 60 samples.\n", "Selected IDs saved to selected_train_ids.json\n", "--- Medical Abstract Evaluation Script ---\n", "Loading preprocessed data...\n", "Preprocessed data loaded.\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "Warning: Config file config_eval.yaml not found. Using default settings.\n", "\n", "--- Loading Selected Training IDs from selected_train_ids.json ---\n", "Filtered training data using selected subset. Shape: (60, 10000)\n", "\n", "--- Starting Model Training (on selected subset) and Evaluation (on full test) ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:09<00:00,  1.08it/s]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.09987009612886463, 0.10049790447338283, 0.09987009612886463, 0.09987009612886463, 0.09992201715622563, 0.09987009612886463, 0.09987009612886463, 0.09987009612886463, 0.10047391501959291, 0.09987009612886463]\n", "Average F1 Macro Score: 0.1000\n", "Standard Deviation of F1 Macro Scores: 0.0002\n", "Metrics saved to results_selected_subset.json\n", "Running for train_size=100\n", "--- Medical TF-IDF Nested Cross-Validation Selection Script ---\n", "Loading preprocessed training data for selection...\n", "Preprocessed data loaded. Full Training data shape: (11550, 10000)\n", "Original Sample IDs shape: (11550,)\n", "Full training data contains 11550 samples across 5 classes.\n", "\n", "--- Starting Medical TF-IDF Nested Cross-Validation Selection Baseline ---\n", "Train set size limit (final selection): 100\n", "Random seed: 42\n", "Nester CV folds: 10\n", "Outer split train proportion: 0.8\n", "\n", "Running Nested Cross-Validation:\n", "Outer CV Fold: 100%|████████████████████████████| 10/10 [03:29<00:00, 20.95s/it]\n", "\n", "Nested CV Selection complete. Best F1 Macro Score found during CV: 0.2280\n", "Selected 100 sample indices based on nested CV performance.\n", "Selected training set created with 100 samples.\n", "  Label 1: 22 samples\n", "  Label 2: 11 samples\n", "  Label 3: 13 samples\n", "  Label 4: 21 samples\n", "  Label 5: 33 samples\n", "\n", "Selection script finished. Selected 100 samples.\n", "Selected IDs saved to selected_train_ids.json\n", "--- Medical Abstract Evaluation Script ---\n", "Loading preprocessed data...\n", "Preprocessed data loaded.\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "Warning: Config file config_eval.yaml not found. Using default settings.\n", "\n", "--- Loading Selected Training IDs from selected_train_ids.json ---\n", "Filtered training data using selected subset. Shape: (100, 10000)\n", "\n", "--- Starting Model Training (on selected subset) and Evaluation (on full test) ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:13<00:00,  1.32s/it]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.20125083280071032, 0.24792761377682745, 0.20041570994763463, 0.1361059920982582, 0.13364274026661532, 0.2419384408247905, 0.1412942771261861, 0.2004588700950193, 0.18929187080163196, 0.09987009612886463]\n", "Average F1 Macro Score: 0.1792\n", "Standard Deviation of F1 Macro Scores: 0.0467\n", "Metrics saved to results_selected_subset.json\n", "Running for train_size=200\n", "--- Medical TF-IDF Nested Cross-Validation Selection Script ---\n", "Loading preprocessed training data for selection...\n", "Preprocessed data loaded. Full Training data shape: (11550, 10000)\n", "Original Sample IDs shape: (11550,)\n", "Full training data contains 11550 samples across 5 classes.\n", "\n", "--- Starting Medical TF-IDF Nested Cross-Validation Selection Baseline ---\n", "Train set size limit (final selection): 200\n", "Random seed: 42\n", "Nester CV folds: 10\n", "Outer split train proportion: 0.8\n", "\n", "Running Nested Cross-Validation:\n", "Outer CV Fold: 100%|████████████████████████████| 10/10 [06:25<00:00, 38.54s/it]\n", "\n", "Nested CV Selection complete. Best F1 Macro Score found during CV: 0.3749\n", "Selected 200 sample indices based on nested CV performance.\n", "Selected training set created with 200 samples.\n", "  Label 1: 44 samples\n", "  Label 2: 21 samples\n", "  Label 3: 27 samples\n", "  Label 4: 42 samples\n", "  Label 5: 66 samples\n", "\n", "Selection script finished. Selected 200 samples.\n", "Selected IDs saved to selected_train_ids.json\n", "--- Medical Abstract Evaluation Script ---\n", "Loading preprocessed data...\n", "Preprocessed data loaded.\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "Warning: Config file config_eval.yaml not found. Using default settings.\n", "\n", "--- Loading Selected Training IDs from selected_train_ids.json ---\n", "Filtered training data using selected subset. Shape: (200, 10000)\n", "\n", "--- Starting Model Training (on selected subset) and Evaluation (on full test) ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:22<00:00,  2.25s/it]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.3437544378769072, 0.3610004425957411, 0.3149265464933766, 0.36804594775297833, 0.341983827257911, 0.32740081481580496, 0.35703769564149346, 0.34320671050475715, 0.35914194705992675, 0.3540653072598311]\n", "Average F1 Macro Score: 0.3471\n", "Standard Deviation of F1 Macro Scores: 0.0155\n", "Metrics saved to results_selected_subset.json\n", "Running for train_size=500\n", "--- Medical TF-IDF Nested Cross-Validation Selection Script ---\n", "Loading preprocessed training data for selection...\n", "Preprocessed data loaded. Full Training data shape: (11550, 10000)\n", "Original Sample IDs shape: (11550,)\n", "Full training data contains 11550 samples across 5 classes.\n", "\n", "--- Starting Medical TF-IDF Nested Cross-Validation Selection Baseline ---\n", "Train set size limit (final selection): 500\n", "Random seed: 42\n", "Nester CV folds: 10\n", "Outer split train proportion: 0.8\n", "\n", "Running Nested Cross-Validation:\n", "Outer CV Fold: 100%|████████████████████████████| 10/10 [16:21<00:00, 98.14s/it]\n", "\n", "Nested CV Selection complete. Best F1 Macro Score found during CV: 0.5177\n", "Selected 500 sample indices based on nested CV performance.\n", "Selected training set created with 500 samples.\n", "  Label 1: 109 samples\n", "  Label 2: 52 samples\n", "  Label 3: 67 samples\n", "  Label 4: 106 samples\n", "  Label 5: 166 samples\n", "\n", "Selection script finished. Selected 500 samples.\n", "Selected IDs saved to selected_train_ids.json\n", "--- Medical Abstract Evaluation Script ---\n", "Loading preprocessed data...\n", "Preprocessed data loaded.\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "Warning: Config file config_eval.yaml not found. Using default settings.\n", "\n", "--- Loading Selected Training IDs from selected_train_ids.json ---\n", "Filtered training data using selected subset. Shape: (500, 10000)\n", "\n", "--- Starting Model Training (on selected subset) and Evaluation (on full test) ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:59<00:00,  5.95s/it]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.4898164460028919, 0.5098385151491157, 0.4969595062218465, 0.48137663948638804, 0.5066681455645402, 0.49999275043677976, 0.49857567173444794, 0.48595030669436523, 0.4799183001014285, 0.5025460735235321]\n", "Average F1 Macro Score: 0.4952\n", "Standard Deviation of F1 Macro Scores: 0.0099\n", "Metrics saved to results_selected_subset.json\n", "Running for train_size=1000\n", "--- Medical TF-IDF Nested Cross-Validation Selection Script ---\n", "Loading preprocessed training data for selection...\n", "Preprocessed data loaded. Full Training data shape: (11550, 10000)\n", "Original Sample IDs shape: (11550,)\n", "Full training data contains 11550 samples across 5 classes.\n", "\n", "--- Starting Medical TF-IDF Nested Cross-Validation Selection Baseline ---\n", "Train set size limit (final selection): 1000\n", "Random seed: 42\n", "Nester CV folds: 10\n", "Outer split train proportion: 0.8\n", "\n", "Running Nested Cross-Validation:\n", "Outer CV Fold: 100%|███████████████████████████| 10/10 [38:02<00:00, 228.22s/it]\n", "\n", "Nested CV Selection complete. Best F1 Macro Score found during CV: 0.5354\n", "Selected 1000 sample indices based on nested CV performance.\n", "Selected training set created with 1000 samples.\n", "  Label 1: 219 samples\n", "  Label 2: 104 samples\n", "  Label 3: 133 samples\n", "  Label 4: 211 samples\n", "  Label 5: 333 samples\n", "\n", "Selection script finished. Selected 1000 samples.\n", "Selected IDs saved to selected_train_ids.json\n", "--- Medical Abstract Evaluation Script ---\n", "Loading preprocessed data...\n", "Preprocessed data loaded.\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "Warning: Config file config_eval.yaml not found. Using default settings.\n", "\n", "--- Loading Selected Training IDs from selected_train_ids.json ---\n", "Filtered training data using selected subset. Shape: (1000, 10000)\n", "\n", "--- Starting Model Training (on selected subset) and Evaluation (on full test) ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [02:42<00:00, 16.24s/it]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.5326040993636687, 0.5216987047006618, 0.511913770527604, 0.5292650391130121, 0.5194196059841869, 0.5096645520777032, 0.5214113228309591, 0.5203702640933197, 0.5141575765741597, 0.5089070803695395]\n", "Average F1 Macro Score: 0.5189\n", "Standard Deviation of F1 Macro Scores: 0.0075\n", "Metrics saved to results_selected_subset.json\n", "Running for train_size=2000\n", "--- Medical TF-IDF Nested Cross-Validation Selection Script ---\n", "Loading preprocessed training data for selection...\n", "Preprocessed data loaded. Full Training data shape: (11550, 10000)\n", "Original Sample IDs shape: (11550,)\n", "Full training data contains 11550 samples across 5 classes.\n", "\n", "--- Starting Medical TF-IDF Nested Cross-Validation Selection Baseline ---\n", "Train set size limit (final selection): 2000\n", "Random seed: 42\n", "Nester CV folds: 10\n", "Outer split train proportion: 0.8\n", "\n", "Running Nested Cross-Validation:\n", "Outer CV Fold:   0%|                                     | 0/10 [00:00<?, ?it/s]"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/IPython/utils/_process_posix.py:156\u001b[0m, in \u001b[0;36mProcessHandler.system\u001b[0;34m(self, cmd)\u001b[0m\n\u001b[1;32m    153\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m    154\u001b[0m     \u001b[38;5;66;03m# res is the index of the pattern that caused the match, so we\u001b[39;00m\n\u001b[1;32m    155\u001b[0m     \u001b[38;5;66;03m# know whether we've finished (if we matched EOF) or not\u001b[39;00m\n\u001b[0;32m--> 156\u001b[0m     res_idx \u001b[38;5;241m=\u001b[39m \u001b[43mchild\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexpect_list\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpatterns\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_timeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    157\u001b[0m     \u001b[38;5;28mprint\u001b[39m(child\u001b[38;5;241m.\u001b[39mbefore[out_size:]\u001b[38;5;241m.\u001b[39mdecode(enc, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mreplace\u001b[39m\u001b[38;5;124m'\u001b[39m), end\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/pexpect/spawnbase.py:383\u001b[0m, in \u001b[0;36mSpawnBase.expect_list\u001b[0;34m(self, pattern_list, timeout, searchwindowsize, async_, **kw)\u001b[0m\n\u001b[1;32m    382\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[0;32m--> 383\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mexp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexpect_loop\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/pexpect/expect.py:169\u001b[0m, in \u001b[0;36mExpecter.expect_loop\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    168\u001b[0m \u001b[38;5;66;03m# Still have time left, so read more data\u001b[39;00m\n\u001b[0;32m--> 169\u001b[0m incoming \u001b[38;5;241m=\u001b[39m \u001b[43mspawn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_nonblocking\u001b[49m\u001b[43m(\u001b[49m\u001b[43mspawn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmaxread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    170\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mspawn\u001b[38;5;241m.\u001b[39mdelayafterread \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n", "File \u001b[0;32m/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/pexpect/pty_spawn.py:500\u001b[0m, in \u001b[0;36mspawn.read_nonblocking\u001b[0;34m(self, size, timeout)\u001b[0m\n\u001b[1;32m    497\u001b[0m \u001b[38;5;66;03m# Because of the select(0) check above, we know that no data\u001b[39;00m\n\u001b[1;32m    498\u001b[0m \u001b[38;5;66;03m# is available right now. But if a non-zero timeout is given\u001b[39;00m\n\u001b[1;32m    499\u001b[0m \u001b[38;5;66;03m# (possibly timeout=None), we call select() with a timeout.\u001b[39;00m\n\u001b[0;32m--> 500\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (timeout \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m) \u001b[38;5;129;01mand\u001b[39;00m \u001b[43mselect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[1;32m    501\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m(spawn, \u001b[38;5;28mself\u001b[39m)\u001b[38;5;241m.\u001b[39mread_nonblocking(size)\n", "File \u001b[0;32m/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/pexpect/pty_spawn.py:450\u001b[0m, in \u001b[0;36mspawn.read_nonblocking.<locals>.select\u001b[0;34m(timeout)\u001b[0m\n\u001b[1;32m    449\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mselect\u001b[39m(timeout):\n\u001b[0;32m--> 450\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mselect_ignore_interrupts\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mchild_fd\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/pexpect/utils.py:143\u001b[0m, in \u001b[0;36mselect_ignore_interrupts\u001b[0;34m(iwtd, owtd, ewtd, timeout)\u001b[0m\n\u001b[1;32m    142\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 143\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mselect\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect\u001b[49m\u001b[43m(\u001b[49m\u001b[43miwtd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mowtd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mewtd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    144\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mInterruptedError\u001b[39;00m:\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: ", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 5\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRunning for train_size=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtrain_size\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      4\u001b[0m \u001b[38;5;66;03m# Run selection\u001b[39;00m\n\u001b[0;32m----> 5\u001b[0m \u001b[43mget_ipython\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msystem\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mpython select_medical.py        --preprocessed_data_dir=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mpreprocessed_data/\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m        --output_file=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mselected_train_ids.json\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m        --train_size=\u001b[39;49m\u001b[38;5;132;43;01m{train_size}\u001b[39;49;00m\u001b[38;5;124;43m        --random_seed=42        --n_folds=10        --outer_split_size=0.8\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;66;03m# Run evaluation\u001b[39;00m\n\u001b[1;32m      8\u001b[0m get_ipython()\u001b[38;5;241m.\u001b[39msystem(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpython eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json selected_train_ids.json --metrics_output_file results_selected_subset.json\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/ipykernel/zmqshell.py:657\u001b[0m, in \u001b[0;36mZMQInteractiveShell.system_piped\u001b[0;34m(self, cmd)\u001b[0m\n\u001b[1;32m    655\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muser_ns[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_exit_code\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m system(cmd)\n\u001b[1;32m    656\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 657\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muser_ns[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_exit_code\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[43msystem\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvar_expand\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcmd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdepth\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/IPython/utils/_process_posix.py:167\u001b[0m, in \u001b[0;36mProcessHandler.system\u001b[0;34m(self, cmd)\u001b[0m\n\u001b[1;32m    162\u001b[0m         out_size \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(child\u001b[38;5;241m.\u001b[39mbefore)\n\u001b[1;32m    163\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m:\n\u001b[1;32m    164\u001b[0m     \u001b[38;5;66;03m# We need to send ^C to the process.  The ascii code for '^C' is 3\u001b[39;00m\n\u001b[1;32m    165\u001b[0m     \u001b[38;5;66;03m# (the character is known as ETX for 'End of Text', see\u001b[39;00m\n\u001b[1;32m    166\u001b[0m     \u001b[38;5;66;03m# curses.ascii.ETX).\u001b[39;00m\n\u001b[0;32m--> 167\u001b[0m     \u001b[43mchild\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msendline\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mchr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m3\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    168\u001b[0m     \u001b[38;5;66;03m# Read and print any more output the program might produce on its\u001b[39;00m\n\u001b[1;32m    169\u001b[0m     \u001b[38;5;66;03m# way out.\u001b[39;00m\n\u001b[1;32m    170\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/pexpect/pty_spawn.py:578\u001b[0m, in \u001b[0;36mspawn.sendline\u001b[0;34m(self, s)\u001b[0m\n\u001b[1;32m    572\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m'''Wraps send(), sending string ``s`` to child process, with\u001b[39;00m\n\u001b[1;32m    573\u001b[0m \u001b[38;5;124;03m``os.linesep`` automatically appended. Returns number of bytes\u001b[39;00m\n\u001b[1;32m    574\u001b[0m \u001b[38;5;124;03mwritten.  Only a limited number of bytes may be sent for each\u001b[39;00m\n\u001b[1;32m    575\u001b[0m \u001b[38;5;124;03mline in the default terminal mode, see docstring of :meth:`send`.\u001b[39;00m\n\u001b[1;32m    576\u001b[0m \u001b[38;5;124;03m'''\u001b[39;00m\n\u001b[1;32m    577\u001b[0m s \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_coerce_send_string(s)\n\u001b[0;32m--> 578\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlinesep\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/drive1/nammt/dataperf-speech-example/venv/lib/python3.10/site-packages/pexpect/pty_spawn.py:563\u001b[0m, in \u001b[0;36mspawn.send\u001b[0;34m(self, s)\u001b[0m\n\u001b[1;32m    528\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m'''Sends string ``s`` to the child process, returning the number of\u001b[39;00m\n\u001b[1;32m    529\u001b[0m \u001b[38;5;124;03mbytes written. If a logfile is specified, a copy is written to that\u001b[39;00m\n\u001b[1;32m    530\u001b[0m \u001b[38;5;124;03mlog.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    559\u001b[0m \u001b[38;5;124;03m    >>> bash.sendline('x' * 5000)\u001b[39;00m\n\u001b[1;32m    560\u001b[0m \u001b[38;5;124;03m'''\u001b[39;00m\n\u001b[1;32m    562\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdelaybeforesend \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m--> 563\u001b[0m     \u001b[43mtime\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msleep\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdelaybeforesend\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    565\u001b[0m s \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_coerce_send_string(s)\n\u001b[1;32m    566\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_log(s, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124msend\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["results = []\n", "for train_size in sizes:\n", "    print(f'Running for train_size={train_size}')\n", "    # Run selection\n", "    !python select_medical.py \\\n", "      --preprocessed_data_dir=\"preprocessed_data/\" \\\n", "      --output_file=\"selected_train_ids.json\" \\\n", "      --train_size={train_size} \\\n", "      --random_seed=42 \\\n", "      --n_folds=10 \\\n", "      --outer_split_size=0.8\n", "\n", "    # Run evaluation\n", "    !python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json selected_train_ids.json --metrics_output_file results_selected_subset.json\n", "\n", "    with open('results_selected_subset.json', 'r') as f:\n", "        metrics = json.load(f)\n", "    metrics['train_size'] = train_size\n", "    results.append(metrics)\n", "\n", "with open('train_size_vs_f1.json', 'w') as f:\n", "    json.dump(results, f, indent=2)\n", "\n", "print('Sweep complete. Results saved to train_size_vs_f1.json')"]}, {"cell_type": "markdown", "id": "56abda16", "metadata": {}, "source": ["### ALL OF TRAIN SET\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "1b983469", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved all 11550 training IDs to selected_train_ids.json\n"]}], "source": ["import numpy as np\n", "import json\n", "\n", "all_ids = np.load('preprocessed_data/train_sample_ids.npy')\n", "\n", "\n", "with open('selected_train_ids.json', 'w') as f:\n", "    json.dump({\"selected_ids\": all_ids.tolist()}, f)\n", "print(f\"Saved all {len(all_ids)} training IDs to selected_train_ids.json\")"]}, {"cell_type": "code", "execution_count": 5, "id": "514472af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Medical Abstract Evaluation Script ---\n", "Loading preprocessed data...\n", "Preprocessed data loaded.\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "Warning: Config file config_eval.yaml not found. Using default settings.\n", "\n", "--- Using Full Training Data (--use_full_training flag set) ---\n", "\n", "--- Training and Evaluating Once on Full Training Set ---\n", "Single Run F1 Macro Score: 0.5087\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.5087079089476615]\n", "Average F1 Macro Score: 0.5087\n", "Standard Deviation of F1 Macro Scores: 0.0000\n", "Metrics saved to metrics.json\n"]}], "source": ["!python eval_medical.py --use_full_training=True"]}, {"cell_type": "code", "execution_count": null, "id": "902c0ff8", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "train_sizes = [25, 60, 100, 200, 500, 1000, 11550]\n", "f1_scores = [0.0999, 0.1000, 0.1792, 0.3471, 0.4952, 0.5189, 0.5087]\n", "std_devs = [0.0000, 0.0002, 0.0467, 0.0155, 0.0099, 0.0075, 0.0000]\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.errorbar(train_sizes, f1_scores, yerr=std_devs, fmt='o-', capsize=5)\n", "\n", "plt.xscale('log')\n", "\n", "plt.xlabel('Training Set Size (log scale)')\n", "plt.y<PERSON><PERSON>('F1 Macro Score')\n", "plt.title('F1 Macro Score vs Training Set Size')\n", "\n", "plt.grid(True, which=\"both\", ls=\"-\", alpha=0.2)\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "1c8f34c2", "metadata": {}, "outputs": [], "source": ["import json\n", "import matplotlib.pyplot as plt\n", "\n", "with open('train_size_vs_f1.json', 'r') as f:\n", "    results = json.load(f)\n", "\n", "train_sizes = [r['train_size'] for r in results]\n", "f1_scores = [r['average_f1_macro'] for r in results]\n", "\n", "plt.figure(figsize=(8,5))\n", "plt.plot(train_sizes, f1_scores, marker='o')\n", "plt.xscale('log')\n", "plt.xlabel('Training Set Size (log scale)')\n", "plt.ylabel('Average F1 Macro')\n", "plt.title('F1 Macro vs. Training Set Size')\n", "plt.grid(True, which='both', ls='--', alpha=0.5)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 3, "id": "589cc7a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading preprocessed data from preprocessed_data/...\n", "Data loaded in 0.11 seconds. Shape: (11550, 10000)\n", "\n", "Selecting 25 samples using cleanlab with 3 folds...\n", "Number of classes: 5\n", "Class distribution target (0-indexed labels):\n", "  Class 1: 5 samples (0-indexed: 0)\n", "  Class 2: 5 samples (0-indexed: 1)\n", "  Class 3: 5 samples (0-indexed: 2)\n", "  Class 4: 5 samples (0-indexed: 3)\n", "  Class 5: 5 samples (0-indexed: 4)\n", "Using cleanlab + K-medoids clustering to select data subset ...\n", "Finding label issues using Cleanlab with 3 folds...\n", "Computing out of sample predicted probabilities via 3-fold cross validation. May take a while ...\n", "Using predicted probabilities to identify label issues ...\n", "Identified 3873 examples with label issues.\n", "Cleanlab find_label_issues completed in 1.93 seconds.\n", "Class distribution after cleanlab filtering: \n", "[[   1 1321]\n", " [   2  492]\n", " [   3  680]\n", " [   4 1262]\n", " [   5 1936]]\n", "Keeping 5691 samples after filtering label issues and low quality scores.\n", "Finding coresets among remaining clean data using K-Medoids ...\n", "Finding coreset for class 1 (0-indexed: 0). Target size: 5, Available after filter: 1321 ...\n", "  Converting sparse data for class 1 (shape (1321, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.21 seconds.\n", "  K-Medoids fitting completed in 0.44 seconds.\n", "Finding coreset for class 2 (0-indexed: 1). Target size: 5, Available after filter: 492 ...\n", "  Converting sparse data for class 2 (shape (492, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.12 seconds.\n", "  K-Medoids fitting completed in 0.09 seconds.\n", "Finding coreset for class 3 (0-indexed: 2). Target size: 5, Available after filter: 680 ...\n", "  Converting sparse data for class 3 (shape (680, 10000)) to dense...\n", "  Dense conversion completed in 0.15 seconds.\n", "  K-Medoids fitting completed in 0.14 seconds.\n", "Finding coreset for class 4 (0-indexed: 3). Target size: 5, Available after filter: 1262 ...\n", "  Converting sparse data for class 4 (shape (1262, 10000)) to dense...\n", "  Den<PERSON> conversion completed in 0.25 seconds.\n", "  K-Medoids fitting completed in 0.36 seconds.\n", "Finding coreset for class 5 (0-indexed: 4). Target size: 5, Available after filter: 1936 ...\n", "  Converting sparse data for class 5 (shape (1936, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.36 seconds.\n", "  K-Medoids fitting completed in 0.73 seconds.\n", "K-Medoids clustering for all classes completed in 2.85 seconds.\n", "Class distribution post-selection (0-indexed labels): \n", "Selected Class Distribution (Original Labels):\n", "  Class 1: 5 selected (Target: 5)\n", "  Class 2: 5 selected (Target: 5)\n", "  Class 3: 5 selected (Target: 5)\n", "  Class 4: 5 selected (Target: 5)\n", "  Class 5: 5 selected (Target: 5)\n", "Selected 25 samples in total.\n", "\n", "Selection process completed in 4.84 seconds.\n", "\n", "Saving selected IDs to Cleanlab/selected_train_ids_cleanlab.json\n", "Saving completed in 0.00 seconds.\n", "\n", "Done! Selected samples distribution (Original Labels):\n", "Class 1: 5 samples\n", "Class 2: 5 samples\n", "Class 3: 5 samples\n", "Class 4: 5 samples\n", "Class 5: 5 samples\n"]}], "source": ["TRAIN_SIZE = 25 \n", "!python Cleanlab/select_cleanlab_medical.py \\\n", "  --preprocessed_data_dir=\"preprocessed_data/\" \\\n", "  --output_file=\"Cleanlab/selected_train_ids_cleanlab.json\" \\\n", "  --train_size={TRAIN_SIZE} \\\n", "  --random_seed=42 \\\n", "  --n_folds=3 \\\n", "  --class_balance=\"equal\" \\\n", "  --extra_frac=0.5"]}, {"cell_type": "code", "execution_count": 4, "id": "5580c560", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Medical Abstract Evaluation Script ---\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "\n", "--- Loading Selected Training IDs from Cleanlab/selected_train_ids_cleanlab.json ---\n", "Filtered training data using selected subset. Shape: (25, 10000)\n", "\n", "--- Training and Evaluating with Multiple Random Seeds ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:03<00:00,  2.83it/s]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.10339098985388376, 0.10705273374591903, 0.2042563463842797, 0.12165282246390108, 0.06140784008937896, 0.3304762547697268, 0.3237566105631549, 0.08002313400531272, 0.13498161009626614, 0.10190286484269728]\n", "Average F1 Macro Score: 0.1569\n", "Standard Deviation of F1 Macro Scores: 0.0924\n", "Metrics saved to Cleanlab/results_selected_subset.json\n"]}], "source": ["!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Cleanlab/selected_train_ids_cleanlab.json --metrics_output_file Cleanlab/results_selected_subset.json"]}, {"cell_type": "code", "execution_count": 22, "id": "a2c8c95e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading preprocessed data from preprocessed_data/...\n", "Data loaded in 0.10 seconds. Shape: (11550, 10000)\n", "\n", "Selecting 25 samples using cleanlab with 3 folds...\n", "Number of classes: 5\n", "Class distribution target (0-indexed labels):\n", "  Class 1: 5 samples (0-indexed: 0)\n", "  Class 2: 5 samples (0-indexed: 1)\n", "  Class 3: 5 samples (0-indexed: 2)\n", "  Class 4: 5 samples (0-indexed: 3)\n", "  Class 5: 5 samples (0-indexed: 4)\n", "Using cleanlab + K-medoids clustering to select data subset ...\n", "Finding label issues using Cleanlab with 3 folds...\n", "Computing out of sample predicted probabilities via 3-fold cross validation. May take a while ...\n", "Using predicted probabilities to identify label issues ...\n", "Identified 3873 examples with label issues.\n", "Cleanlab find_label_issues completed in 1.83 seconds.\n", "Class distribution after cleanlab filtering: \n", "[[   1 1321]\n", " [   2  492]\n", " [   3  680]\n", " [   4 1262]\n", " [   5 1936]]\n", "Keeping 5691 samples after filtering label issues and low quality scores.\n", "Finding coresets among remaining clean data using K-Medoids ...\n", "Finding coreset for class 1 (0-indexed: 0). Target size: 5, Available after filter: 1321 ...\n", "  Converting sparse data for class 1 (shape (1321, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.21 seconds.\n", "  K-Medoids fitting completed in 0.40 seconds.\n", "Finding coreset for class 2 (0-indexed: 1). Target size: 5, Available after filter: 492 ...\n", "  Converting sparse data for class 2 (shape (492, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.12 seconds.\n", "  K-Medoids fitting completed in 0.09 seconds.\n", "Finding coreset for class 3 (0-indexed: 2). Target size: 5, Available after filter: 680 ...\n", "  Converting sparse data for class 3 (shape (680, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.16 seconds.\n", "  K-Medoids fitting completed in 0.13 seconds.\n", "Finding coreset for class 4 (0-indexed: 3). Target size: 5, Available after filter: 1262 ...\n", "  Converting sparse data for class 4 (shape (1262, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.26 seconds.\n", "  K-Medoids fitting completed in 0.57 seconds.\n", "Finding coreset for class 5 (0-indexed: 4). Target size: 5, Available after filter: 1936 ...\n", "  Converting sparse data for class 5 (shape (1936, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.36 seconds.\n", "  K-Medoids fitting completed in 1.02 seconds.\n", "K-Medoids clustering for all classes completed in 3.33 seconds.\n", "Class distribution post-selection (0-indexed labels): \n", "Selected Class Distribution (Original Labels):\n", "  Class 1: 5 selected (Target: 5)\n", "  Class 2: 5 selected (Target: 5)\n", "  Class 3: 5 selected (Target: 5)\n", "  Class 4: 5 selected (Target: 5)\n", "  Class 5: 5 selected (Target: 5)\n", "Selected 25 samples in total.\n", "\n", "Selection process completed in 5.19 seconds.\n", "\n", "Saving selected IDs to selected_train_ids_cleanlab.json\n", "Saving completed in 0.00 seconds.\n", "\n", "Done! Selected samples distribution (Original Labels):\n", "Class 1: 5 samples\n", "Class 2: 5 samples\n", "Class 3: 5 samples\n", "Class 4: 5 samples\n", "Class 5: 5 samples\n", "--- Medical Abstract Evaluation Script ---\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "\n", "--- Loading Selected Training IDs from selected_train_ids_cleanlab.json ---\n", "Filtered training data using selected subset. Shape: (25, 10000)\n", "\n", "--- Training and Evaluating with Multiple Random Seeds ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:04<00:00,  2.31it/s]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.10339098985388376, 0.10705273374591903, 0.2042563463842797, 0.12165282246390108, 0.06140784008937896, 0.3304762547697268, 0.3237566105631549, 0.08002313400531272, 0.13498161009626614, 0.10190286484269728]\n", "Average F1 Macro Score: 0.1569\n", "Standard Deviation of F1 Macro Scores: 0.0924\n", "Metrics saved to results_selected_subset.json\n", "Loading preprocessed data from preprocessed_data/...\n", "Data loaded in 0.10 seconds. Shape: (11550, 10000)\n", "\n", "Selecting 60 samples using cleanlab with 3 folds...\n", "Number of classes: 5\n", "Class distribution target (0-indexed labels):\n", "  Class 1: 12 samples (0-indexed: 0)\n", "  Class 2: 12 samples (0-indexed: 1)\n", "  Class 3: 12 samples (0-indexed: 2)\n", "  Class 4: 12 samples (0-indexed: 3)\n", "  Class 5: 12 samples (0-indexed: 4)\n", "Using cleanlab + K-medoids clustering to select data subset ...\n", "Finding label issues using Cleanlab with 3 folds...\n", "Computing out of sample predicted probabilities via 3-fold cross validation. May take a while ...\n", "Using predicted probabilities to identify label issues ...\n", "Identified 3873 examples with label issues.\n", "Cleanlab find_label_issues completed in 1.84 seconds.\n", "Class distribution after cleanlab filtering: \n", "[[   1 1321]\n", " [   2  492]\n", " [   3  680]\n", " [   4 1262]\n", " [   5 1936]]\n", "Keeping 5691 samples after filtering label issues and low quality scores.\n", "Finding coresets among remaining clean data using K-Medoids ...\n", "Finding coreset for class 1 (0-indexed: 0). Target size: 12, Available after filter: 1321 ...\n", "  Converting sparse data for class 1 (shape (1321, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.21 seconds.\n", "  K-Medoids fitting completed in 0.38 seconds.\n", "Finding coreset for class 2 (0-indexed: 1). Target size: 12, Available after filter: 492 ...\n", "  Converting sparse data for class 2 (shape (492, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.12 seconds.\n", "  K-Medoids fitting completed in 0.11 seconds.\n", "Finding coreset for class 3 (0-indexed: 2). Target size: 12, Available after filter: 680 ...\n", "  Converting sparse data for class 3 (shape (680, 10000)) to dense...\n", "  Dense conversion completed in 0.15 seconds.\n", "  K-Medoids fitting completed in 0.13 seconds.\n", "Finding coreset for class 4 (0-indexed: 3). Target size: 12, Available after filter: 1262 ...\n", "  Converting sparse data for class 4 (shape (1262, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.27 seconds.\n", "  K-Medoids fitting completed in 0.34 seconds.\n", "Finding coreset for class 5 (0-indexed: 4). Target size: 12, Available after filter: 1936 ...\n", "  Converting sparse data for class 5 (shape (1936, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.37 seconds.\n", "  K-Medoids fitting completed in 0.69 seconds.\n", "K-Medoids clustering for all classes completed in 2.78 seconds.\n", "Class distribution post-selection (0-indexed labels): \n", "Selected Class Distribution (Original Labels):\n", "  Class 1: 12 selected (Target: 12)\n", "  Class 2: 12 selected (Target: 12)\n", "  Class 3: 12 selected (Target: 12)\n", "  Class 4: 12 selected (Target: 12)\n", "  Class 5: 12 selected (Target: 12)\n", "Selected 60 samples in total.\n", "\n", "Selection process completed in 4.66 seconds.\n", "\n", "Saving selected IDs to selected_train_ids_cleanlab.json\n", "Saving completed in 0.00 seconds.\n", "\n", "Done! Selected samples distribution (Original Labels):\n", "Class 1: 12 samples\n", "Class 2: 12 samples\n", "Class 3: 12 samples\n", "Class 4: 12 samples\n", "Class 5: 12 samples\n", "--- Medical Abstract Evaluation Script ---\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "\n", "--- Loading Selected Training IDs from selected_train_ids_cleanlab.json ---\n", "Filtered training data using selected subset. Shape: (60, 10000)\n", "\n", "--- Training and Evaluating with Multiple Random Seeds ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:08<00:00,  1.22it/s]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.21819780312680725, 0.2163748983753382, 0.22096425924832333, 0.4486106410008371, 0.21491962197828246, 0.41310098250848204, 0.5228969809168303, 0.434379840294069, 0.510202090952147, 0.5161049914643597]\n", "Average F1 Macro Score: 0.3716\n", "Standard Deviation of F1 Macro Scores: 0.1302\n", "Metrics saved to results_selected_subset.json\n", "Loading preprocessed data from preprocessed_data/...\n", "Data loaded in 0.10 seconds. Shape: (11550, 10000)\n", "\n", "Selecting 100 samples using cleanlab with 3 folds...\n", "Number of classes: 5\n", "Class distribution target (0-indexed labels):\n", "  Class 1: 20 samples (0-indexed: 0)\n", "  Class 2: 20 samples (0-indexed: 1)\n", "  Class 3: 20 samples (0-indexed: 2)\n", "  Class 4: 20 samples (0-indexed: 3)\n", "  Class 5: 20 samples (0-indexed: 4)\n", "Using cleanlab + K-medoids clustering to select data subset ...\n", "Finding label issues using Cleanlab with 3 folds...\n", "Computing out of sample predicted probabilities via 3-fold cross validation. May take a while ...\n", "Using predicted probabilities to identify label issues ...\n", "Identified 3873 examples with label issues.\n", "Cleanlab find_label_issues completed in 1.91 seconds.\n", "Class distribution after cleanlab filtering: \n", "[[   1 1321]\n", " [   2  492]\n", " [   3  680]\n", " [   4 1262]\n", " [   5 1936]]\n", "Keeping 5691 samples after filtering label issues and low quality scores.\n", "Finding coresets among remaining clean data using K-Medoids ...\n", "Finding coreset for class 1 (0-indexed: 0). Target size: 20, Available after filter: 1321 ...\n", "  Converting sparse data for class 1 (shape (1321, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.21 seconds.\n", "  K-Medoids fitting completed in 0.38 seconds.\n", "Finding coreset for class 2 (0-indexed: 1). Target size: 20, Available after filter: 492 ...\n", "  Converting sparse data for class 2 (shape (492, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.12 seconds.\n", "  K-Medoids fitting completed in 0.10 seconds.\n", "Finding coreset for class 3 (0-indexed: 2). Target size: 20, Available after filter: 680 ...\n", "  Converting sparse data for class 3 (shape (680, 10000)) to dense...\n", "  Dense conversion completed in 0.15 seconds.\n", "  K-Medoids fitting completed in 0.14 seconds.\n", "Finding coreset for class 4 (0-indexed: 3). Target size: 20, Available after filter: 1262 ...\n", "  Converting sparse data for class 4 (shape (1262, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.26 seconds.\n", "  K-Medoids fitting completed in 0.35 seconds.\n", "Finding coreset for class 5 (0-indexed: 4). Target size: 20, Available after filter: 1936 ...\n", "  Converting sparse data for class 5 (shape (1936, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.37 seconds.\n", "  K-Medoids fitting completed in 0.69 seconds.\n", "K-Medoids clustering for all classes completed in 2.78 seconds.\n", "Class distribution post-selection (0-indexed labels): \n", "Selected Class Distribution (Original Labels):\n", "  Class 1: 20 selected (Target: 20)\n", "  Class 2: 20 selected (Target: 20)\n", "  Class 3: 20 selected (Target: 20)\n", "  Class 4: 20 selected (Target: 20)\n", "  Class 5: 20 selected (Target: 20)\n", "Selected 100 samples in total.\n", "\n", "Selection process completed in 4.73 seconds.\n", "\n", "Saving selected IDs to selected_train_ids_cleanlab.json\n", "Saving completed in 0.00 seconds.\n", "\n", "Done! Selected samples distribution (Original Labels):\n", "Class 1: 20 samples\n", "Class 2: 20 samples\n", "Class 3: 20 samples\n", "Class 4: 20 samples\n", "Class 5: 20 samples\n", "--- Medical Abstract Evaluation Script ---\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "\n", "--- Loading Selected Training IDs from selected_train_ids_cleanlab.json ---\n", "Filtered training data using selected subset. Shape: (100, 10000)\n", "\n", "--- Training and Evaluating with Multiple Random Seeds ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:12<00:00,  1.24s/it]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.5561337052405058, 0.5011187726993204, 0.5038033347314956, 0.5043925476950933, 0.5154087930903114, 0.5174492423841446, 0.5551831747800801, 0.5216435672216632, 0.5557730486787454, 0.524384540105564]\n", "Average F1 Macro Score: 0.5255\n", "Standard Deviation of F1 Macro Scores: 0.0210\n", "Metrics saved to results_selected_subset.json\n", "Loading preprocessed data from preprocessed_data/...\n", "Data loaded in 0.10 seconds. Shape: (11550, 10000)\n", "\n", "Selecting 200 samples using cleanlab with 3 folds...\n", "Number of classes: 5\n", "Class distribution target (0-indexed labels):\n", "  Class 1: 40 samples (0-indexed: 0)\n", "  Class 2: 40 samples (0-indexed: 1)\n", "  Class 3: 40 samples (0-indexed: 2)\n", "  Class 4: 40 samples (0-indexed: 3)\n", "  Class 5: 40 samples (0-indexed: 4)\n", "Using cleanlab + K-medoids clustering to select data subset ...\n", "Finding label issues using Cleanlab with 3 folds...\n", "Computing out of sample predicted probabilities via 3-fold cross validation. May take a while ...\n", "Using predicted probabilities to identify label issues ...\n", "Identified 3873 examples with label issues.\n", "Cleanlab find_label_issues completed in 2.07 seconds.\n", "Class distribution after cleanlab filtering: \n", "[[   1 1321]\n", " [   2  492]\n", " [   3  680]\n", " [   4 1262]\n", " [   5 1936]]\n", "Keeping 5691 samples after filtering label issues and low quality scores.\n", "Finding coresets among remaining clean data using K-Medoids ...\n", "Finding coreset for class 1 (0-indexed: 0). Target size: 40, Available after filter: 1321 ...\n", "  Converting sparse data for class 1 (shape (1321, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.21 seconds.\n", "  K-Medoids fitting completed in 0.40 seconds.\n", "Finding coreset for class 2 (0-indexed: 1). Target size: 40, Available after filter: 492 ...\n", "  Converting sparse data for class 2 (shape (492, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.12 seconds.\n", "  K-Medoids fitting completed in 0.12 seconds.\n", "Finding coreset for class 3 (0-indexed: 2). Target size: 40, Available after filter: 680 ...\n", "  Converting sparse data for class 3 (shape (680, 10000)) to dense...\n", "  Dense conversion completed in 0.15 seconds.\n", "  K-Medoids fitting completed in 0.15 seconds.\n", "Finding coreset for class 4 (0-indexed: 3). Target size: 40, Available after filter: 1262 ...\n", "  Converting sparse data for class 4 (shape (1262, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.27 seconds.\n", "  K-Medoids fitting completed in 0.38 seconds.\n", "Finding coreset for class 5 (0-indexed: 4). Target size: 40, Available after filter: 1936 ...\n", "  Converting sparse data for class 5 (shape (1936, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.36 seconds.\n", "  K-Medoids fitting completed in 1.60 seconds.\n", "K-Medoids clustering for all classes completed in 3.76 seconds.\n", "Class distribution post-selection (0-indexed labels): \n", "Selected Class Distribution (Original Labels):\n", "  Class 1: 40 selected (Target: 40)\n", "  Class 2: 40 selected (Target: 40)\n", "  Class 3: 40 selected (Target: 40)\n", "  Class 4: 40 selected (Target: 40)\n", "  Class 5: 40 selected (Target: 40)\n", "Selected 200 samples in total.\n", "\n", "Selection process completed in 5.88 seconds.\n", "\n", "Saving selected IDs to selected_train_ids_cleanlab.json\n", "Saving completed in 0.00 seconds.\n", "\n", "Done! Selected samples distribution (Original Labels):\n", "Class 1: 40 samples\n", "Class 2: 40 samples\n", "Class 3: 40 samples\n", "Class 4: 40 samples\n", "Class 5: 40 samples\n", "--- Medical Abstract Evaluation Script ---\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "\n", "--- Loading Selected Training IDs from selected_train_ids_cleanlab.json ---\n", "Filtered training data using selected subset. Shape: (200, 10000)\n", "\n", "--- Training and Evaluating with Multiple Random Seeds ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:25<00:00,  2.56s/it]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.5703317188562961, 0.5731378593232409, 0.5729453877778667, 0.5682856047760974, 0.5589623742210021, 0.5638476696814573, 0.5634018370949166, 0.573812008792013, 0.5700929397393273, 0.5679334195700824]\n", "Average F1 Macro Score: 0.5683\n", "Standard Deviation of F1 Macro Scores: 0.0046\n", "Metrics saved to results_selected_subset.json\n", "Loading preprocessed data from preprocessed_data/...\n", "Data loaded in 0.10 seconds. Shape: (11550, 10000)\n", "\n", "Selecting 500 samples using cleanlab with 3 folds...\n", "Number of classes: 5\n", "Class distribution target (0-indexed labels):\n", "  Class 1: 100 samples (0-indexed: 0)\n", "  Class 2: 100 samples (0-indexed: 1)\n", "  Class 3: 100 samples (0-indexed: 2)\n", "  Class 4: 100 samples (0-indexed: 3)\n", "  Class 5: 100 samples (0-indexed: 4)\n", "Using cleanlab + K-medoids clustering to select data subset ...\n", "Finding label issues using Cleanlab with 3 folds...\n", "Computing out of sample predicted probabilities via 3-fold cross validation. May take a while ...\n", "Using predicted probabilities to identify label issues ...\n", "Identified 3873 examples with label issues.\n", "Cleanlab find_label_issues completed in 1.86 seconds.\n", "Class distribution after cleanlab filtering: \n", "[[   1 1321]\n", " [   2  492]\n", " [   3  680]\n", " [   4 1262]\n", " [   5 1936]]\n", "Keeping 5691 samples after filtering label issues and low quality scores.\n", "Finding coresets among remaining clean data using K-Medoids ...\n", "Finding coreset for class 1 (0-indexed: 0). Target size: 100, Available after filter: 1321 ...\n", "  Converting sparse data for class 1 (shape (1321, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.21 seconds.\n", "  K-Medoids fitting completed in 0.46 seconds.\n", "Finding coreset for class 2 (0-indexed: 1). Target size: 100, Available after filter: 492 ...\n", "  Converting sparse data for class 2 (shape (492, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.12 seconds.\n", "  K-Medoids fitting completed in 0.15 seconds.\n", "Finding coreset for class 3 (0-indexed: 2). Target size: 100, Available after filter: 680 ...\n", "  Converting sparse data for class 3 (shape (680, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.16 seconds.\n", "  K-Medoids fitting completed in 0.20 seconds.\n", "Finding coreset for class 4 (0-indexed: 3). Target size: 100, Available after filter: 1262 ...\n", "  Converting sparse data for class 4 (shape (1262, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.26 seconds.\n", "  K-Medoids fitting completed in 0.40 seconds.\n", "Finding coreset for class 5 (0-indexed: 4). Target size: 100, Available after filter: 1936 ...\n", "  Converting sparse data for class 5 (shape (1936, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.37 seconds.\n", "  K-Medoids fitting completed in 0.79 seconds.\n", "K-Medoids clustering for all classes completed in 3.13 seconds.\n", "Class distribution post-selection (0-indexed labels): \n", "Selected Class Distribution (Original Labels):\n", "  Class 1: 100 selected (Target: 100)\n", "  Class 2: 100 selected (Target: 100)\n", "  Class 3: 100 selected (Target: 100)\n", "  Class 4: 100 selected (Target: 100)\n", "  Class 5: 100 selected (Target: 100)\n", "Selected 500 samples in total.\n", "\n", "Selection process completed in 5.03 seconds.\n", "\n", "Saving selected IDs to selected_train_ids_cleanlab.json\n", "Saving completed in 0.00 seconds.\n", "\n", "Done! Selected samples distribution (Original Labels):\n", "Class 1: 100 samples\n", "Class 2: 100 samples\n", "Class 3: 100 samples\n", "Class 4: 100 samples\n", "Class 5: 100 samples\n", "--- Medical Abstract Evaluation Script ---\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "\n", "--- Loading Selected Training IDs from selected_train_ids_cleanlab.json ---\n", "Filtered training data using selected subset. Shape: (500, 10000)\n", "\n", "--- Training and Evaluating with Multiple Random Seeds ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [00:55<00:00,  5.55s/it]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.5888162189890351, 0.5867738275177976, 0.5888488187461922, 0.5895144456274292, 0.587397858895449, 0.5880174704650811, 0.5885233275970758, 0.5904431947918756, 0.5897235527249652, 0.5889532503591235]\n", "Average F1 Macro Score: 0.5887\n", "Standard Deviation of F1 Macro Scores: 0.0010\n", "Metrics saved to results_selected_subset.json\n", "Loading preprocessed data from preprocessed_data/...\n", "Data loaded in 0.10 seconds. Shape: (11550, 10000)\n", "\n", "Selecting 1000 samples using cleanlab with 3 folds...\n", "Number of classes: 5\n", "Class distribution target (0-indexed labels):\n", "  Class 1: 200 samples (0-indexed: 0)\n", "  Class 2: 200 samples (0-indexed: 1)\n", "  Class 3: 200 samples (0-indexed: 2)\n", "  Class 4: 200 samples (0-indexed: 3)\n", "  Class 5: 200 samples (0-indexed: 4)\n", "Using cleanlab + K-medoids clustering to select data subset ...\n", "Finding label issues using Cleanlab with 3 folds...\n", "Computing out of sample predicted probabilities via 3-fold cross validation. May take a while ...\n", "Using predicted probabilities to identify label issues ...\n", "Identified 3873 examples with label issues.\n", "Cleanlab find_label_issues completed in 1.97 seconds.\n", "Class distribution after cleanlab filtering: \n", "[[   1 1321]\n", " [   2  492]\n", " [   3  680]\n", " [   4 1262]\n", " [   5 1936]]\n", "Keeping 5691 samples after filtering label issues and low quality scores.\n", "Finding coresets among remaining clean data using K-Medoids ...\n", "Finding coreset for class 1 (0-indexed: 0). Target size: 200, Available after filter: 1321 ...\n", "  Converting sparse data for class 1 (shape (1321, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.21 seconds.\n", "  K-Medoids fitting completed in 0.54 seconds.\n", "Finding coreset for class 2 (0-indexed: 1). Target size: 200, Available after filter: 492 ...\n", "  Converting sparse data for class 2 (shape (492, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.13 seconds.\n", "  K-Medoids fitting completed in 0.19 seconds.\n", "Finding coreset for class 3 (0-indexed: 2). Target size: 200, Available after filter: 680 ...\n", "  Converting sparse data for class 3 (shape (680, 10000)) to dense...\n", "  Dense conversion completed in 0.15 seconds.\n", "  K-Medoids fitting completed in 0.24 seconds.\n", "Finding coreset for class 4 (0-indexed: 3). Target size: 200, Available after filter: 1262 ...\n", "  Converting sparse data for class 4 (shape (1262, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.26 seconds.\n", "  K-Medoids fitting completed in 0.49 seconds.\n", "Finding coreset for class 5 (0-indexed: 4). Target size: 200, Available after filter: 1936 ...\n", "  Converting sparse data for class 5 (shape (1936, 10000)) to dense...\n", "  <PERSON><PERSON> conversion completed in 0.36 seconds.\n", "  K-Medoids fitting completed in 0.88 seconds.\n", "K-Medoids clustering for all classes completed in 3.46 seconds.\n", "Class distribution post-selection (0-indexed labels): \n", "Selected Class Distribution (Original Labels):\n", "  Class 1: 200 selected (Target: 200)\n", "  Class 2: 200 selected (Target: 200)\n", "  Class 3: 200 selected (Target: 200)\n", "  Class 4: 200 selected (Target: 200)\n", "  Class 5: 200 selected (Target: 200)\n", "Selected 1000 samples in total.\n", "\n", "Selection process completed in 5.47 seconds.\n", "\n", "Saving selected IDs to selected_train_ids_cleanlab.json\n", "Saving completed in 0.00 seconds.\n", "\n", "Done! Selected samples distribution (Original Labels):\n", "Class 1: 200 samples\n", "Class 2: 200 samples\n", "Class 3: 200 samples\n", "Class 4: 200 samples\n", "Class 5: 200 samples\n", "--- Medical Abstract Evaluation Script ---\n", "Full Training data shape: (11550, 10000), Labels shape: (11550,), IDs shape: (11550,)\n", "Test data shape: (2888, 10000), Labels shape: (2888,)\n", "\n", "--- Loading Selected Training IDs from selected_train_ids_cleanlab.json ---\n", "Filtered training data using selected subset. Shape: (1000, 10000)\n", "\n", "--- Training and Evaluating with Multiple Random Seeds ---\n", "Evaluating Seeds: 100%|█████████████████████████| 10/10 [02:35<00:00, 15.54s/it]\n", "\n", "--- Evaluation Results ---\n", "Individual F1 Macro Scores: [0.6026492259463183, 0.6047566492108032, 0.6033572312327233, 0.6058942136681942, 0.6045165948712677, 0.6043782579885429, 0.6040941705956859, 0.605080046056451, 0.6062729030315854, 0.6054560596027423]\n", "Average F1 Macro Score: 0.6046\n", "Standard Deviation of F1 Macro Scores: 0.0011\n", "Metrics saved to results_selected_subset.json\n", "\n", "Sweep complete! Detailed log saved to cleanlab_sweep_results.txt\n"]}], "source": ["import json\n", "import numpy as np\n", "\n", "log_file = f'cleanlab_sweep_results.txt'\n", "\n", "results = []\n", "with open(log_file, 'w') as f:\n", "    f.write(\"=\" * 80 + \"\\n\\n\")\n", "    \n", "    for train_size in fixed_small_sizes:\n", "        f.write(\"=\" * 80 + \"\\n\")\n", "        \n", "        # Run cleanlab selection\n", "        !python select_cleanlab_medical.py \\\n", "          --preprocessed_data_dir=\"preprocessed_data/\" \\\n", "          --output_file=\"selected_train_ids_cleanlab.json\" \\\n", "          --train_size={train_size} \\\n", "          --random_seed=42 \\\n", "          --n_folds=3 \\\n", "          --class_balance=\"equal\" \\\n", "          --extra_frac=0.5\n", "    \n", "        with open('selected_train_ids_cleanlab.json', 'r') as rf:\n", "            selected_data = json.load(rf)\n", "            selected_ids = np.array(selected_data['selected_ids'])\n", "            \n", "        # Get label counts\n", "        labels = np.load('preprocessed_data/y_train_full.npy')[selected_ids]\n", "        unique, counts = np.unique(labels, return_counts=True)\n", "        \n", "        # Write class distribution\n", "        f.write(f\"\\nSelected training set created with {train_size} samples.\\n\")\n", "        for label, count in zip(unique, counts):\n", "            f.write(f\"  Label {label}: {count} samples\\n\")\n", "        \n", "        # Run evaluation\n", "        !python eval_medical.py \\\n", "          --preprocessed_data_dir preprocessed_data/ \\\n", "          --selected_ids_json selected_train_ids_cleanlab.json \\\n", "          --metrics_output_file results_selected_subset.json\n", "        \n", "        # Store and log evaluation results\n", "        with open('results_selected_subset.json', 'r') as rf:\n", "            metrics = json.load(rf)\n", "        \n", "        f.write(\"--- Evaluation Results ---\\n\")\n", "        # Safely access metrics with fallback values\n", "        f.write(f\"Individual F1 Macro Scores: {metrics.get('f1_macro_scores', metrics.get('individual_f1_scores', []))}\\n\")\n", "        f.write(f\"Average F1 Macro Score: {metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0)):.4f}\\n\")\n", "        f.write(f\"Standard Deviation of F1 Macro Scores: {metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0)):.4f}\\n\")\n", "        \n", "        # Add blank lines for readability\n", "        f.write(\"\\n\\n\")\n", "        f.flush()  # Force write to disk\n", "\n", "print(f'\\nSweep complete! Detailed log saved to {log_file}')"]}, {"cell_type": "code", "execution_count": 23, "id": "cf8c7cee", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Original data\n", "train_sizes_orig = [25, 60, 100, 200, 500, 1000, 11550]\n", "f1_scores_orig = [0.0999, 0.1000, 0.1792, 0.3471, 0.4952, 0.5189, 0.5087]\n", "std_devs_orig = [0.0000, 0.0002, 0.0467, 0.0155, 0.0099, 0.0075, 0.0000]\n", "\n", "# Cleanlab data (including full dataset)\n", "train_sizes_cleanlab = [25, 60, 100, 200, 500, 1000, 11550]\n", "f1_scores_cleanlab = [0.1569, 0.3716, 0.5255, 0.5683, 0.5887, 0.6046, 0.5087]\n", "std_devs_cleanlab = [0.0924, 0.1302, 0.0210, 0.0046, 0.0010, 0.0011, 0.0000]\n", "\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Plot original data\n", "plt.errorbar(train_sizes_orig, f1_scores_orig, yerr=std_devs_orig, \n", "             fmt='o-', capsize=5, label='Random Selection', color='blue')\n", "\n", "# Plot cleanlab data\n", "plt.errorbar(train_sizes_cleanlab, f1_scores_cleanlab, yerr=std_devs_cleanlab, \n", "             fmt='s-', capsize=5, label='Cleanlab Selection', color='red')\n", "\n", "plt.xscale('log')\n", "plt.grid(True, which=\"both\", ls=\"-\", alpha=0.2)\n", "\n", "plt.xlabel('Training Set Size (log scale)')\n", "plt.y<PERSON><PERSON>('F1 Macro Score')\n", "plt.title('F1 Macro Score vs Training Size: Random vs Cleanlab Selection')\n", "plt.legend()\n", "\n", "# Add value labels for both curves\n", "for x, y in zip(train_sizes_orig, f1_scores_orig):\n", "    plt.annotate(f'{y:.3f}', (x, y), textcoords=\"offset points\", \n", "                xytext=(0,10), ha='center', color='blue')\n", "for x, y in zip(train_sizes_cleanlab, f1_scores_cleanlab):\n", "    plt.annotate(f'{y:.3f}', (x, y), textcoords=\"offset points\", \n", "                xytext=(0,-15), ha='center', color='red')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "099f4bff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}