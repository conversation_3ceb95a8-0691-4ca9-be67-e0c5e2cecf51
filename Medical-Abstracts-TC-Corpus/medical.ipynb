TRAIN_SIZE = 25
!python Baseline/select_medical.py \
  --preprocessed_data_dir="preprocessed_data/" \
  --output_file="Baseline/selected_train_ids.json" \
  --train_size={TRAIN_SIZE} \
  --random_seed=42 \
  --n_folds=10 \
  --outer_split_size=0.8

!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Baseline/selected_train_ids.json --metrics_output_file Baseline/results_selected_subset.json



import os
import json
import numpy as np
import matplotlib.pyplot as plt

train_ids_path = os.path.join('preprocessed_data', 'train_sample_ids.npy')
train_sample_ids = np.load(train_ids_path)
max_train_size = len(train_sample_ids)

max_train_size

fixed_small_sizes = [25, 60, 100, 200, 500, 1000]



results = []
for train_size in fixed_small_sizes:
    print(f'Running for train_size={train_size}')
    # Run selection
    !python select_medical.py \
      --preprocessed_data_dir="preprocessed_data/" \
      --output_file="selected_train_ids.json" \
      --train_size={train_size} \
      --random_seed=42 \
      --n_folds=10 \
      --outer_split_size=0.8

    # Run evaluation
    !python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json selected_train_ids.json --metrics_output_file results_selected_subset.json

    with open('results_selected_subset.json', 'r') as f:
        metrics = json.load(f)
    metrics['train_size'] = train_size
    results.append(metrics)

with open('train_size_vs_f1.json', 'w') as f:
    json.dump(results, f, indent=2)

print('Sweep complete. Results saved to train_size_vs_f1.json')

import numpy as np
import json

all_ids = np.load('preprocessed_data/train_sample_ids.npy')


with open('selected_train_ids.json', 'w') as f:
    json.dump({"selected_ids": all_ids.tolist()}, f)
print(f"Saved all {len(all_ids)} training IDs to selected_train_ids.json")

!python eval_medical.py --use_full_training=True

import numpy as np
import matplotlib.pyplot as plt

train_sizes = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores = [0.0999, 0.1000, 0.1792, 0.3471, 0.4952, 0.5189, 0.5087]
std_devs = [0.0000, 0.0002, 0.0467, 0.0155, 0.0099, 0.0075, 0.0000]

plt.figure(figsize=(10, 6))
plt.errorbar(train_sizes, f1_scores, yerr=std_devs, fmt='o-', capsize=5)

plt.xscale('log')

plt.xlabel('Training Set Size (log scale)')
plt.ylabel('F1 Macro Score')
plt.title('F1 Macro Score vs Training Set Size')

plt.grid(True, which="both", ls="-", alpha=0.2)

plt.show()

import json
import matplotlib.pyplot as plt

with open('train_size_vs_f1.json', 'r') as f:
    results = json.load(f)

train_sizes = [r['train_size'] for r in results]
f1_scores = [r['average_f1_macro'] for r in results]

plt.figure(figsize=(8,5))
plt.plot(train_sizes, f1_scores, marker='o')
plt.xscale('log')
plt.xlabel('Training Set Size (log scale)')
plt.ylabel('Average F1 Macro')
plt.title('F1 Macro vs. Training Set Size')
plt.grid(True, which='both', ls='--', alpha=0.5)
plt.tight_layout()
plt.show()

TRAIN_SIZE = 25 
!python Cleanlab/select_cleanlab_medical.py \
  --preprocessed_data_dir="preprocessed_data/" \
  --output_file="Cleanlab/selected_train_ids_cleanlab.json" \
  --train_size={TRAIN_SIZE} \
  --random_seed=42 \
  --n_folds=3 \
  --class_balance="equal" \
  --extra_frac=0.5

!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Cleanlab/selected_train_ids_cleanlab.json --metrics_output_file Cleanlab/results_selected_subset.json

pip install "numpy<2"

import json
import numpy as np

fixed_small_sizes = [25, 60, 100, 200, 500, 1000]


log_file = f'cleanlab_sweep_results.txt'

results = []
with open(log_file, 'w') as f:
    f.write("=" * 80 + "\n\n")
    
    for train_size in fixed_small_sizes:
        f.write("=" * 80 + "\n")
        
        # Run cleanlab selection
        !python Cleanlab/select_cleanlab_medical.py \
          --preprocessed_data_dir="preprocessed_data/" \
          --output_file="Cleanlab/selected_train_ids_cleanlab.json" \
          --train_size={train_size} \
          --random_seed=42 \
          --n_folds=3 \
          --class_balance="equal" \
          --extra_frac=0.5
    
        with open('Cleanlab/selected_train_ids_cleanlab.json', 'r') as rf:
            selected_data = json.load(rf)
            selected_ids = np.array(selected_data['selected_ids'])
            

        labels = np.load('preprocessed_data/y_train_full.npy')[selected_ids]
        unique, counts = np.unique(labels, return_counts=True)
        
        f.write(f"\nSelected training set created with {train_size} samples.\n")
        for label, count in zip(unique, counts):
            f.write(f"  Label {label}: {count} samples\n")
        
   
        !python eval_medical.py \
          --preprocessed_data_dir preprocessed_data/ \
          --selected_ids_json Cleanlab/selected_train_ids_cleanlab.json \
          --metrics_output_file results_selected_subset.json
        

        with open('results_selected_subset.json', 'r') as rf:
            metrics = json.load(rf)
        
        f.write("--- Evaluation Results ---\n")
        f.write(f"Individual F1 Macro Scores: {metrics.get('f1_macro_scores', metrics.get('individual_f1_scores', []))}\n")
        f.write(f"Average F1 Macro Score: {metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0)):.4f}\n")
        f.write(f"Standard Deviation of F1 Macro Scores: {metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0)):.4f}\n")
        
      
        f.write("\n\n")
        f.flush()  # Force write to disk

print(f'\nSweep complete! Detailed log saved to {log_file}')

import numpy as np
import matplotlib.pyplot as plt


train_sizes_orig = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_orig = [0.0999, 0.1000, 0.1792, 0.3471, 0.4952, 0.5189, 0.5087]
std_devs_orig = [0.0000, 0.0002, 0.0467, 0.0155, 0.0099, 0.0075, 0.0000]

# Cleanlab data (including full dataset)
train_sizes_cleanlab = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_cleanlab = [0.1569, 0.3716, 0.5255, 0.5683, 0.5887, 0.6046, 0.5087]
std_devs_cleanlab = [0.0924, 0.1302, 0.0210, 0.0046, 0.0010, 0.0011, 0.0000]

plt.figure(figsize=(12, 8))


plt.errorbar(train_sizes_orig, f1_scores_orig, yerr=std_devs_orig, 
             fmt='o-', capsize=5, label='Random Selection', color='blue')

# Plot cleanlab data
plt.errorbar(train_sizes_cleanlab, f1_scores_cleanlab, yerr=std_devs_cleanlab, 
             fmt='s-', capsize=5, label='Cleanlab Selection', color='red')

plt.xscale('log')
plt.grid(True, which="both", ls="-", alpha=0.2)

plt.xlabel('Training Set Size (log scale)')
plt.ylabel('F1 Macro Score')
plt.title('F1 Macro Score vs Training Size: Random vs Cleanlab Selection')
plt.legend()


for x, y in zip(train_sizes_orig, f1_scores_orig):
    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                xytext=(0,10), ha='center', color='blue')
for x, y in zip(train_sizes_cleanlab, f1_scores_cleanlab):
    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                xytext=(0,-15), ha='center', color='red')

plt.tight_layout()
plt.show()

# Add this as a new cell in your medical.ipynb notebook
# DIAGNOSTIC VERSION - Let's see what's actually in the files

import numpy as np
import pandas as pd
import scipy.sparse
import os

print("="*80)
print("DIAGNOSTIC: Checking data files")
print("="*80)

# Check what files exist
print("Files in preprocessed_data/:")
for file in os.listdir("preprocessed_data/"):
    if file.endswith(('.npy', '.npz')):
        filepath = f"preprocessed_data/{file}"
        size = os.path.getsize(filepath)
        print(f"  {file}: {size:,} bytes")

print("\n" + "="*60)
print("LOADING AND INSPECTING DATA")
print("="*60)

# Load y_train_full first (this should work)
print("Loading y_train_full...")
y_train_full = np.load("preprocessed_data/y_train_full.npy")
print(f"y_train_full shape: {y_train_full.shape}")
print(f"y_train_full type: {type(y_train_full)}")
print(f"Classes: {np.unique(y_train_full)}")

# Load train_sample_ids
print("\nLoading train_sample_ids...")
train_sample_ids = np.load("preprocessed_data/train_sample_ids.npy")
print(f"train_sample_ids shape: {train_sample_ids.shape}")
print(f"train_sample_ids type: {type(train_sample_ids)}")

# Now let's carefully inspect X_train_full_vec
print("\nInspecting X_train_full_vec.npz...")
try:
    # Try loading as npz archive first
    data = np.load("preprocessed_data/X_train_full_vec.npz")
    print(f"NPZ archive keys: {list(data.files)}")
    
    for key in data.files:
        arr = data[key]
        print(f"  {key}: shape={arr.shape}, dtype={arr.dtype}, type={type(arr)}")
        
    # Get the main array (usually the first one)
    main_key = data.files[0]
    X_train_full_vec = data[main_key]
    data.close()
    
except Exception as e:
    print(f"Error loading as npz: {e}")
    try:
        # Try loading as sparse matrix
        X_train_full_vec = scipy.sparse.load_npz("preprocessed_data/X_train_full_vec.npz")
        print(f"Loaded as sparse matrix: {type(X_train_full_vec)}")
    except Exception as e2:
        print(f"Error loading as sparse: {e2}")
        X_train_full_vec = None

if X_train_full_vec is not None:
    print(f"\nFinal X_train_full_vec:")
    print(f"  Type: {type(X_train_full_vec)}")
    print(f"  Shape: {X_train_full_vec.shape}")
    if hasattr(X_train_full_vec, 'dtype'):
        print(f"  Dtype: {X_train_full_vec.dtype}")
    
    # Check if shapes match
    print(f"\nShape consistency check:")
    print(f"  X_train_full_vec samples: {X_train_full_vec.shape[0]}")
    print(f"  y_train_full samples: {y_train_full.shape[0]}")
    print(f"  train_sample_ids samples: {train_sample_ids.shape[0]}")
    
    shapes_match = (X_train_full_vec.shape[0] == y_train_full.shape[0] == train_sample_ids.shape[0])
    print(f"  All shapes match: {shapes_match}")
    
    if shapes_match:
        print("\n✓ Data looks good! Proceeding with analysis...")
        
        # Load abstracts
        try:
            with open('preprocessed_data/train_abstracts.txt', 'r', encoding='utf-8') as f:
                abstracts = [line.strip() for line in f.readlines()]
            print(f"✓ Loaded {len(abstracts)} abstracts")
            abstracts_match = len(abstracts) == X_train_full_vec.shape[0]
            print(f"  Abstracts count matches: {abstracts_match}")
        except Exception as e:
            print(f"✗ Could not load abstracts: {e}")
            abstracts = None
        
        print("\n" + "="*60)
        print("QUICK DUPLICATE CHECK")
        print("="*60)
        
        # Quick check for duplicates in a small sample
        sample_size = min(100, X_train_full_vec.shape[0])
        print(f"Checking first {sample_size} samples for duplicates...")
        
        feature_vector_map = {}
        duplicate_count = 0
        
        for idx in range(sample_size):
            if scipy.sparse.issparse(X_train_full_vec):
                feature_vector = X_train_full_vec[idx].toarray().tobytes()
            else:
                feature_vector = X_train_full_vec[idx].tobytes()
            
            label = y_train_full[idx]
            
            if feature_vector in feature_vector_map:
                prev_idx, prev_label = feature_vector_map[feature_vector]
                if prev_label != label:
                    duplicate_count += 1
                    print(f"  Found duplicate: samples {prev_idx} and {idx} have same features but different labels ({prev_label} vs {label})")
                    if abstracts:
                        text1 = abstracts[prev_idx]
                        text2 = abstracts[idx]
                        print(f"    Texts identical: {text1 == text2}")
                        print(f"    Text: {text1[:100]}...")
            else:
                feature_vector_map[feature_vector] = (idx, label)
        
        print(f"\nFound {duplicate_count} duplicate pairs in first {sample_size} samples")
        
        if duplicate_count == 0:
            print("No true duplicates found in the sample.")
            print("This suggests that cleanlab's 'duplicate_issues.txt' contains")
            print("label quality issues, not actual duplicates.")
    else:
        print("\n✗ Shape mismatch! Cannot proceed with analysis.")
else:
    print("\n✗ Could not load X_train_full_vec")

print("\n" + "="*60)
print("CONCLUSION")
print("="*60)
print("Based on this diagnostic:")
print("1. If no true duplicates were found, then cleanlab's 'duplicate_issues.txt'")
print("   contains samples with poor label quality, not actual duplicates")
print("2. Cleanlab identifies potentially mislabeled samples based on model predictions")
print("3. True duplicates would be samples with identical feature vectors but different labels")
print("4. The distinction is important for understanding what cleanlab actually does")

# Add this as a new cell in your medical.ipynb notebook
# FINAL ANALYSIS: Cleanlab results vs true duplicates

import numpy as np
import pandas as pd
import scipy.sparse
from cleanlab.classification import CleanLearning
import sklearn.linear_model

def load_sparse_csr_matrix(file_path):
    """Load CSR sparse matrix from npz file"""
    loader = np.load(file_path)
    matrix = scipy.sparse.csr_matrix((loader['data'], loader['indices'], loader['indptr']), 
                                     shape=loader['shape'])
    loader.close()
    return matrix

print("="*80)
print("CLEANLAB ANALYSIS: Label Issues vs True Duplicates")
print("="*80)

# Load the data properly
print("Loading data...")
X_train_full_vec = load_sparse_csr_matrix("preprocessed_data/X_train_full_vec.npz")
y_train_full = np.load("preprocessed_data/y_train_full.npy")
train_sample_ids = np.load("preprocessed_data/train_sample_ids.npy")

print(f"✓ Loaded sparse matrix: {X_train_full_vec.shape[0]} samples, {X_train_full_vec.shape[1]} features")
print(f"✓ Matrix format: {type(X_train_full_vec)}")
print(f"✓ Classes: {np.unique(y_train_full)}")

# Load abstracts
try:
    with open('preprocessed_data/train_abstracts.txt', 'r', encoding='utf-8') as f:
        abstracts = [line.strip() for line in f.readlines()]
    print(f"✓ Loaded {len(abstracts)} abstracts")
except Exception as e:
    print(f"✗ Could not load abstracts: {e}")
    abstracts = None

print("\n" + "="*60)
print("ANALYSIS 1: Searching for TRUE DUPLICATES")
print("="*60)
print("True duplicates = identical feature vectors with different labels")

# Search for true duplicates
duplicate_pairs = []
feature_vector_map = {}

# Use a reasonable sample size for analysis
sample_size = min(2000, X_train_full_vec.shape[0])
print(f"Analyzing {sample_size} samples for true duplicates...")

for idx in range(sample_size):
    if idx % 500 == 0:
        print(f"  Processed {idx}/{sample_size} samples...")
    
    # Get feature vector as bytes for comparison
    feature_vector = X_train_full_vec[idx].toarray().tobytes()
    label = y_train_full[idx]
    
    if feature_vector in feature_vector_map:
        prev_idx, prev_label = feature_vector_map[feature_vector]
        if prev_label != label:
            # Found a true duplicate!
            duplicate_pairs.append({
                'idx1': prev_idx,
                'id1': train_sample_ids[prev_idx],
                'label1': prev_label,
                'idx2': idx,
                'id2': train_sample_ids[idx],
                'label2': label,
                'text1': abstracts[prev_idx] if abstracts else "Text not available",
                'text2': abstracts[idx] if abstracts else "Text not available"
            })
    else:
        feature_vector_map[feature_vector] = (idx, label)

print(f"\n🔍 RESULT: Found {len(duplicate_pairs)} true duplicate pairs")

if duplicate_pairs:
    print("\n📋 TRUE DUPLICATE PAIRS:")
    for i, pair in enumerate(duplicate_pairs[:3]):  # Show first 3
        print(f"\nPair {i+1}:")
        print(f"  Sample 1: ID {pair['id1']}, Label {pair['label1']}")
        print(f"  Sample 2: ID {pair['id2']}, Label {pair['label2']}")
        if abstracts:
            texts_identical = pair['text1'] == pair['text2']
            print(f"  Texts identical: {texts_identical}")
            print(f"  Text: {pair['text1'][:150]}...")
            if not texts_identical:
                print(f"  Text 2: {pair['text2'][:150]}...")
else:
    print("✅ No true duplicates found!")
    print("   This means no samples have identical feature vectors with different labels.")

print("\n" + "="*60)
print("ANALYSIS 2: What CLEANLAB actually identifies")
print("="*60)

# Run cleanlab on a subset
subset_size = min(1000, X_train_full_vec.shape[0])
X_subset = X_train_full_vec[:subset_size]
y_subset = y_train_full[:subset_size]

# Convert labels to 0-indexed for cleanlab
unique_y = np.unique(y_subset)
label_map = {old: new for new, old in enumerate(unique_y)}
y_zero_indexed = np.array([label_map[y] for y in y_subset])

print(f"Running cleanlab on {subset_size} samples...")
clf = sklearn.linear_model.LogisticRegression(solver='liblinear', random_state=42, max_iter=1000)
cl = CleanLearning(clf, seed=42, verbose=False, cv_n_folds=3)

issues = cl.find_label_issues(X_subset, y_zero_indexed)
cleanlab_issues = issues[issues['is_label_issue']]

print(f"\n🔍 RESULT: Cleanlab identified {len(cleanlab_issues)} label issues")

if len(cleanlab_issues) > 0:
    print(f"\n📋 CLEANLAB LABEL ISSUES (first 3):")
    for i, idx in enumerate(cleanlab_issues.index[:3]):
        label = y_subset[idx]
        sample_id = train_sample_ids[idx]
        quality_score = issues.loc[idx, 'label_quality']
        print(f"\nIssue {i+1}:")
        print(f"  Sample ID: {sample_id}, Label: {label}")
        print(f"  Quality Score: {quality_score:.3f}")
        if abstracts:
            print(f"  Text: {abstracts[idx][:150]}...")

print("\n" + "="*60)
print("ANALYSIS 3: Examining existing 'duplicate_issues.txt'")
print("="*60)

try:
    with open('Cleanlab/duplicate_issues.txt', 'r') as f:
        content = f.read()
    
    # Count lines that start with "Sample ID:"
    sample_lines = [line for line in content.split('\n') if line.startswith('Sample ID:')]
    print(f"📄 Found {len(sample_lines)} entries in duplicate_issues.txt")
    
    if sample_lines:
        print("\n📋 FIRST FEW ENTRIES FROM duplicate_issues.txt:")
        for i, line in enumerate(sample_lines[:3]):
            print(f"  {i+1}. {line}")
    
except FileNotFoundError:
    print("📄 File 'Cleanlab/duplicate_issues.txt' not found")

print("\n" + "="*60)
print("🎯 FINAL CONCLUSIONS")
print("="*60)

print(f"1. TRUE DUPLICATES FOUND: {len(duplicate_pairs)}")
if len(duplicate_pairs) == 0:
    print("   ✅ No samples have identical feature vectors with different labels")
    print("   ✅ This means the dataset doesn't have true duplicates")
else:
    print(f"   ⚠️  Found {len(duplicate_pairs)} pairs with identical features but different labels")

print(f"\n2. CLEANLAB LABEL ISSUES: {len(cleanlab_issues)}")
print("   📊 These are samples that appear mislabeled based on model predictions")
print("   📊 NOT the same as true duplicates")

print(f"\n3. WHAT'S IN 'duplicate_issues.txt':")
print("   📝 Contains cleanlab's label quality issues")
print("   📝 NOT true duplicates (identical texts with different labels)")
print("   📝 These are samples the model thinks might be mislabeled")

print(f"\n4. KEY INSIGHT:")
print("   🔑 Cleanlab identifies POTENTIALLY MISLABELED samples")
print("   🔑 This is different from finding IDENTICAL TEXTS with different labels")
print("   🔑 The current logic in cleanlab_selection.py was treating these as duplicates")

print(f"\n5. RECOMMENDATION:")
print("   💡 The 'duplicate_issues.txt' should be renamed to 'label_quality_issues.txt'")
print("   💡 True duplicate detection would need a different approach")
print("   💡 Consider text-based similarity instead of feature vector comparison")

if len(duplicate_pairs) > 0:
    print(f"\n📁 Saving {len(duplicate_pairs)} true duplicates to 'actual_duplicates_found.txt'")
    with open('actual_duplicates_found.txt', 'w') as f:
        f.write("ACTUAL DUPLICATE PAIRS FOUND\n")
        f.write("="*50 + "\n\n")
        for i, pair in enumerate(duplicate_pairs):
            f.write(f"Pair {i+1}:\n")
            f.write(f"  Sample 1: ID {pair['id1']}, Label {pair['label1']}\n")
            f.write(f"  Sample 2: ID {pair['id2']}, Label {pair['label2']}\n")
            f.write(f"  Texts identical: {pair['text1'] == pair['text2']}\n")
            f.write(f"  Text 1: {pair['text1']}\n")
            f.write(f"  Text 2: {pair['text2']}\n")
            f.write("\n" + "-"*50 + "\n\n")

print("\n✅ Analysis complete!")
print(f"📊 Analyzed {sample_size} samples for true duplicates")
print(f"📊 Ran cleanlab on {subset_size} samples")
print("🎯 Now you understand the difference between cleanlab issues and true duplicates!")




import pandas as pd
import numpy as np
import json
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn_extra.cluster import KMedoids
import scipy.sparse
from collections import Counter
import time

def find_and_remove_duplicates(df, text_column='medical_abstract', label_column='condition_label'):
    print("🔍 Finding duplicates (identical text with different labels)...")
    
    # Group by text to find duplicates
    text_groups = df.groupby(text_column)
    
    duplicate_info = []
    indices_to_remove = set()
    
    for text, group in text_groups:
        if len(group) > 1:  # Multiple samples with same text
            unique_labels = group[label_column].unique()
            if len(unique_labels) > 1:  # Different labels for same text
                duplicate_info.append({
                    'text': text[:200] + "..." if len(text) > 200 else text,
                    'labels': list(unique_labels),
                    'sample_ids': list(group.index),
                    'count': len(group)
                })
                # Remove all instances of this duplicate text
                indices_to_remove.update(group.index)
    
    print(f"📊 Found {len(duplicate_info)} duplicate text groups with different labels")
    print(f"🗑️  Removing {len(indices_to_remove)} samples total")
    
    if duplicate_info:
        print("\n📋 Duplicate examples:")
        for i, dup in enumerate(duplicate_info[:3]):
            print(f"  {i+1}. Text: {dup['text']}")
            print(f"     Labels: {dup['labels']}")
            print(f"     Count: {dup['count']}")
    
    # Create cleaned dataframe
    cleaned_df = df.drop(indices_to_remove).reset_index(drop=True)
    
    print(f"\n📈 Dataset size: {len(df)} -> {len(cleaned_df)} (removed {len(df) - len(cleaned_df)} samples)")
    
    return cleaned_df, duplicate_info

def preprocess_clean_data(df, text_column='abstractText', label_column='condition', max_features=10000):
    """Preprocess the cleaned data with TF-IDF vectorization."""
    print(f"🔧 Preprocessing data with TF-IDF (max_features={max_features})...")
    
    texts = df[text_column].values
    labels = df[label_column].values
    sample_ids = df.index.values
    
    # TF-IDF vectorization
    vectorizer = TfidfVectorizer(
        max_features=max_features,
        stop_words='english',
        lowercase=True,
        ngram_range=(1, 2),
        min_df=2,
        max_df=0.95
    )
    
    X = vectorizer.fit_transform(texts)
    
    print(f"✅ Vectorized to {X.shape[0]} samples x {X.shape[1]} features")
    print(f"📊 Matrix sparsity: {(1 - X.nnz / (X.shape[0] * X.shape[1])):.3f}")
    
    return X, labels, sample_ids, vectorizer

def balanced_kmedoids_selection(X, labels, sample_ids, target_size, class_balance='equal', random_seed=42):
    """Select a balanced subset using K-medoids clustering."""
    print(f"🎯 Running balanced K-medoids selection for {target_size} samples...")
    
    unique_classes = np.unique(labels)
    num_classes = len(unique_classes)
    
    print(f"📊 Classes: {unique_classes}")
    print(f"📊 Original class distribution:")
    for cls in unique_classes:
        count = np.sum(labels == cls)
        print(f"  Class {cls}: {count} samples")
    
    # Determine per-class target sizes
    if class_balance == 'equal':
        per_class_size = target_size // num_classes
        class_sizes = {cls: per_class_size for cls in unique_classes}
        # Distribute remaining samples
        remaining = target_size - (per_class_size * num_classes)
        for i in range(remaining):
            cls = unique_classes[i % num_classes]
            class_sizes[cls] += 1
    else:  # proportional
        class_counts = {cls: np.sum(labels == cls) for cls in unique_classes}
        total_samples = len(labels)
        class_sizes = {
            cls: max(1, int(target_size * (count / total_samples)))
            for cls, count in class_counts.items()
        }
    
    print(f"🎯 Target class distribution:")
    for cls in unique_classes:
        print(f"  Class {cls}: {class_sizes[cls]} samples")
    
    # Select samples for each class using K-medoids
    selected_indices = []
    
    for cls in unique_classes:
        target_count = class_sizes[cls]
        if target_count <= 0:
            continue
            
        class_indices = np.where(labels == cls)[0]
        available_count = len(class_indices)
        
        print(f"⚙️  Selecting {target_count} from {available_count} samples of class {cls}...")
        
        if target_count >= available_count:
            selected_indices.extend(class_indices)
        else:
            # Use K-medoids
            X_class = X[class_indices]
            
            if scipy.sparse.issparse(X_class):
                X_class_dense = X_class.toarray()
            else:
                X_class_dense = X_class
            
            kmedoids = KMedoids(n_clusters=target_count, init="k-medoids++", random_state=random_seed)
            kmedoids.fit(X_class_dense)
            
            selected_class_indices = class_indices[kmedoids.medoid_indices_]
            selected_indices.extend(selected_class_indices)
    
    selected_indices = np.array(selected_indices)
    selected_sample_ids = sample_ids[selected_indices]
    
    print(f"✅ Selected {len(selected_indices)} samples total")
    print("📊 Final class distribution:")
    selected_labels = labels[selected_indices]
    for cls in unique_classes:
        count = np.sum(selected_labels == cls)
        print(f"  Class {cls}: {count} samples")
    
    return selected_indices, selected_sample_ids

# =============================================================================
# MAIN EXECUTION
# =============================================================================

print("="*80)
print("🧹 CLEAN DATASET + K-MEDOIDS SELECTION")
print("="*80)

# Configuration
INPUT_FILE = 'medical_train.csv'
OUTPUT_FILE = 'selected_train_ids_clean_kmedoids.json'
TRAIN_SIZE = 1000
MAX_FEATURES = 10000
CLASS_BALANCE = 'equal'  # or 'proportional'
RANDOM_SEED = 42
TEXT_COLUMN = 'abstractText'
LABEL_COLUMN = 'condition'

# Load data
print(f"📂 Loading data from {INPUT_FILE}...")
df = pd.read_csv(INPUT_FILE)
print(f"✅ Loaded {len(df)} samples")

# Find and remove duplicates
cleaned_df, duplicate_info = find_and_remove_duplicates(df, TEXT_COLUMN, LABEL_COLUMN)

# Save duplicate info
duplicate_info_file = OUTPUT_FILE.replace('.json', '_duplicates_removed.json')
with open(duplicate_info_file, 'w') as f:
    json.dump(duplicate_info, f, indent=2)
print(f"💾 Saved duplicate info to {duplicate_info_file}")

# Preprocess cleaned data
X, labels, sample_ids, vectorizer = preprocess_clean_data(
    cleaned_df, TEXT_COLUMN, LABEL_COLUMN, MAX_FEATURES
)

# Run K-medoids selection
selected_indices, selected_sample_ids = balanced_kmedoids_selection(
    X, labels, sample_ids, TRAIN_SIZE, CLASS_BALANCE, RANDOM_SEED
)

# Save results
results = {
    'selected_ids': selected_sample_ids.tolist(),
    'method': 'clean_kmedoids',
    'parameters': {
        'train_size': TRAIN_SIZE,
        'max_features': MAX_FEATURES,
        'class_balance': CLASS_BALANCE,
        'random_seed': RANDOM_SEED,
        'duplicates_removed': len(duplicate_info),
        'original_size': len(df),
        'cleaned_size': len(cleaned_df)
    }
}

with open(OUTPUT_FILE, 'w') as f:
    json.dump(results, f, indent=2)

print(f"\n💾 Results saved to {OUTPUT_FILE}")
print("="*80)
print("📋 SUMMARY")
print("="*80)
print(f"📊 Original dataset: {len(df)} samples")
print(f"🗑️  Duplicates removed: {len(duplicate_info)} groups ({len(df) - len(cleaned_df)} samples)")
print(f"🧹 Cleaned dataset: {len(cleaned_df)} samples")
print(f"🎯 Selected subset: {len(selected_sample_ids)} samples")
print(f"⚙️  Selection method: K-medoids with {CLASS_BALANCE} class balance")
print(f"✅ Clean selection complete!")

# Display some statistics
if duplicate_info:
    print(f"\n🔍 DUPLICATE ANALYSIS:")
    print(f"   Found {len(duplicate_info)} text groups with conflicting labels")
    print(f"   These represent genuine labeling inconsistencies in the dataset")
    print(f"   Removing them ensures clean training data")
else:
    print(f"\n✅ NO DUPLICATES FOUND:")
    print(f"   Dataset appears to be clean of text-label conflicts")


# Add this as a new cell in your medical.ipynb notebook
# CLEAN K-MEDOIDS SWEEP EVALUATION

import json
import numpy as np
import os
import time

# Configuration
fixed_small_sizes = [25, 60, 100, 200, 500, 1000]
preprocessed_data_dir = "preprocessed_data_clean/"
log_file = 'clean_kmedoids_sweep_results.txt'

print("="*80)
print("🧹 CLEAN K-MEDOIDS SWEEP EVALUATION")
print("="*80)

# First, run preprocessing if needed
if not os.path.exists(preprocessed_data_dir):
    print("🔧 Running preprocessing...")
    !python clean_preprocess_medical.py \
      --input_dir='' \
      --output_dir=preprocessed_data_clean/ \
      --train_file=medical_train.csv \
      --test_file=medical_test.csv \
      --max_features=10000
    print("✅ Preprocessing completed")
else:
    print(f"✅ Using existing preprocessed data in {preprocessed_data_dir}")

# Create CleanKmedoids directory
os.makedirs("CleanKmedoids", exist_ok=True)

results = []

with open(log_file, 'w') as f:
    f.write("="*80 + "\n")
    f.write("CLEAN K-MEDOIDS SWEEP EVALUATION RESULTS\n")
    f.write("="*80 + "\n\n")
    f.write(f"Evaluation started at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    f.write(f"Dataset: Clean medical abstracts (duplicates removed)\n")
    f.write(f"Method: K-medoids clustering with equal class balance\n")
    f.write(f"Sizes evaluated: {fixed_small_sizes}\n\n")
    
    for train_size in fixed_small_sizes:
        f.write("="*80 + "\n")
        f.write(f"EVALUATING TRAIN SIZE: {train_size}\n")
        f.write("="*80 + "\n")
        
        print(f"\n🎯 Evaluating train size: {train_size}")
        
        # Run clean K-medoids selection
        print("  🔄 Running K-medoids selection...")
        
        !python CleanKmedoids/select_clean_kmedoids.py \
          --preprocessed_data_dir={preprocessed_data_dir} \
          --output_file=CleanKmedoids/selected_train_ids_clean_kmedoids.json \
          --train_size={train_size} \
          --random_seed=42 \
          --class_balance=equal
        
        # Load and display selected IDs info
        try:
            with open('CleanKmedoids/selected_train_ids_clean_kmedoids.json', 'r') as rf:
                selected_data = json.load(rf)
                selected_ids = np.array(selected_data['selected_ids'])
            
            # Load labels to check distribution
            labels = np.load(f'{preprocessed_data_dir}/y_train_full.npy')[selected_ids]
            unique, counts = np.unique(labels, return_counts=True)
            
            f.write(f"\nSelected training set created with {train_size} samples.\n")
            for label, count in zip(unique, counts):
                f.write(f"  Label {label}: {count} samples\n")
            
            print(f"  ✅ Selected {len(selected_ids)} samples")
            
        except Exception as e:
            error_msg = f"❌ Error loading selection results: {e}"
            print(error_msg)
            f.write(f"{error_msg}\n\n")
            continue
        
        # Run evaluation
        print("  🔄 Running evaluation...")
        
        !python eval_medical.py \
          --preprocessed_data_dir {preprocessed_data_dir} \
          --selected_ids_json CleanKmedoids/selected_train_ids_clean_kmedoids.json \
          --metrics_output_file results_selected_subset.json
        
        # Load and display metrics
        try:
            with open('results_selected_subset.json', 'r') as rf:
                metrics = json.load(rf)
            
            f.write("--- Evaluation Results ---\n")
            f.write(f"Individual F1 Macro Scores: {metrics.get('individual_scores', metrics.get('f1_macro_scores', []))}\n")
            f.write(f"Average F1 Macro Score: {metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0)):.4f}\n")
            f.write(f"Standard Deviation of F1 Macro Scores: {metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0)):.4f}\n")
            
            # Store results for summary
            results.append({
                'train_size': train_size,
                'avg_f1': metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0)),
                'std_f1': metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0)),
                'individual_scores': metrics.get('individual_scores', metrics.get('f1_macro_scores', []))
            })
            
            print(f"  ✅ F1 Score: {metrics.get('average_f1_macro', 0.0):.4f} ± {metrics.get('std_f1_macro', 0.0):.4f}")
            
        except Exception as e:
            error_msg = f"❌ Error loading evaluation results: {e}"
            print(error_msg)
            f.write(f"{error_msg}\n")
        
        f.write("\n\n")
        f.flush()  # Force write to disk
    
    # Write summary
    f.write("="*80 + "\n")
    f.write("SUMMARY OF ALL RESULTS\n")
    f.write("="*80 + "\n")
    
    if results:
        f.write("Train Size | Avg F1 Score | Std F1 Score\n")
        f.write("-"*40 + "\n")
        for result in results:
            f.write(f"{result['train_size']:9d} | {result['avg_f1']:11.4f} | {result['std_f1']:11.4f}\n")
        
        # Find best result
        best_result = max(results, key=lambda x: x['avg_f1'])
        f.write(f"\nBest performance: {best_result['avg_f1']:.4f} ± {best_result['std_f1']:.4f} at size {best_result['train_size']}\n")
    else:
        f.write("No successful evaluations completed.\n")
    
    f.write(f"\nEvaluation completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")

print(f'\n✅ Sweep complete! Detailed log saved to {log_file}')

# Display summary
if results:
    print("\n📊 SUMMARY:")
    print("Train Size | Avg F1 Score | Std F1 Score")
    print("-"*40)
    for result in results:
        print(f"{result['train_size']:9d} | {result['avg_f1']:11.4f} | {result['std_f1']:11.4f}")
    
    best_result = max(results, key=lambda x: x['avg_f1'])
    print(f"\n🏆 Best: {best_result['avg_f1']:.4f} ± {best_result['std_f1']:.4f} at size {best_result['train_size']}")
    
    # Compare with cleanlab if available
    try:
        with open('cleanlab_sweep_results.txt', 'r') as f:
            cleanlab_content = f.read()
        print(f"\n📋 Cleanlab results file found for comparison")
        print(f"   Check {log_file} vs cleanlab_sweep_results.txt")
    except FileNotFoundError:
        print(f"\n📋 No cleanlab results found for comparison")
        print(f"   Run cleanlab sweep first to compare approaches")
else:
    print("❌ No successful evaluations completed")

print(f"\n📁 Results saved to: {log_file}")
print(f"📁 Clean preprocessed data in: {preprocessed_data_dir}")
print(f"📁 Selection outputs in: CleanKmedoids/")
print("\n🎯 This approach removes true duplicates before selection, ensuring clean training data!")


