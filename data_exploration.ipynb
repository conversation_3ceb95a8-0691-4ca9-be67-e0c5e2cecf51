{"cells": [{"cell_type": "code", "execution_count": 2, "id": "98758299", "metadata": {}, "outputs": [], "source": ["import os, yaml\n", "import numpy as np\n", "from glob import glob\n", "from pathlib import Path\n", "from sklearn.manifold import TSNE\n", "import matplotlib.pyplot as plt\n"]}, {"cell_type": "code", "execution_count": null, "id": "4c980fed", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "script_dir = Path(\"dataperf-speech-example/\").parent.resolve()\n", "\n", "# Traverse upwards from the script directory until a .git directory is found\n", "def find_repo_root(current_dir):\n", "    while current_dir != current_dir.parent: # Stop at the filesystem root\n", "        if (current_dir / \".git\").exists():\n", "            return current_dir\n", "        current_dir = current_dir.parent\n", "    return None \n", "\n", "REPO_ROOT = find_repo_root(script_dir)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "e1663b95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/drive1/nammt/dataperf-speech-example\n"]}], "source": ["print(REPO_ROOT)"]}, {"cell_type": "code", "execution_count": 9, "id": "42ef582c", "metadata": {}, "outputs": [], "source": ["EMBEDDINGS_DIR = REPO_ROOT / \"dataperf-speech-example/workspace/data/dataperf_en_data/train_embeddings\"\n", "ALLOWED_YAML   = REPO_ROOT / \"dataperf-speech-example/workspace/data/dataperf_en_data/allowed_training_set.yaml\"\n"]}, {"cell_type": "code", "execution_count": 10, "id": "56d4c9e2", "metadata": {}, "outputs": [], "source": ["with open(ALLOWED_YAML, \"r\") as f:\n", "    allowed = yaml.safe_load(f)"]}, {"cell_type": "code", "execution_count": null, "id": "5a470228", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}