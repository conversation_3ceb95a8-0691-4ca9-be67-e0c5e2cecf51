TRAIN_SIZE = 25
!python Baseline/select_medical.py \
  --preprocessed_data_dir="preprocessed_data/" \
  --output_file="Baseline/selected_train_ids.json" \
  --train_size={TRAIN_SIZE} \
  --random_seed=42 \
  --n_folds=10 \
  --outer_split_size=0.8

!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Baseline/selected_train_ids.json --metrics_output_file Baseline/results_selected_subset.json



import os
import json
import numpy as np
import matplotlib.pyplot as plt

train_ids_path = os.path.join('preprocessed_data', 'train_sample_ids.npy')
train_sample_ids = np.load(train_ids_path)
max_train_size = len(train_sample_ids)

max_train_size

fixed_small_sizes = [25, 60, 100, 200, 500, 1000]



results = []
for train_size in fixed_small_sizes:
    print(f'Running for train_size={train_size}')
    # Run selection
    !python select_medical.py \
      --preprocessed_data_dir="preprocessed_data/" \
      --output_file="selected_train_ids.json" \
      --train_size={train_size} \
      --random_seed=42 \
      --n_folds=10 \
      --outer_split_size=0.8

    # Run evaluation
    !python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json selected_train_ids.json --metrics_output_file results_selected_subset.json

    with open('results_selected_subset.json', 'r') as f:
        metrics = json.load(f)
    metrics['train_size'] = train_size
    results.append(metrics)

with open('train_size_vs_f1.json', 'w') as f:
    json.dump(results, f, indent=2)

print('Sweep complete. Results saved to train_size_vs_f1.json')

import numpy as np
import json

all_ids = np.load('preprocessed_data/train_sample_ids.npy')


with open('selected_train_ids.json', 'w') as f:
    json.dump({"selected_ids": all_ids.tolist()}, f)
print(f"Saved all {len(all_ids)} training IDs to selected_train_ids.json")

!python eval_medical.py --use_full_training=True

import numpy as np
import matplotlib.pyplot as plt

train_sizes = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores = [0.0999, 0.1000, 0.1792, 0.3471, 0.4952, 0.5189, 0.5087]
std_devs = [0.0000, 0.0002, 0.0467, 0.0155, 0.0099, 0.0075, 0.0000]

plt.figure(figsize=(10, 6))
plt.errorbar(train_sizes, f1_scores, yerr=std_devs, fmt='o-', capsize=5)

plt.xscale('log')

plt.xlabel('Training Set Size (log scale)')
plt.ylabel('F1 Macro Score')
plt.title('F1 Macro Score vs Training Set Size')

plt.grid(True, which="both", ls="-", alpha=0.2)

plt.show()

import json
import matplotlib.pyplot as plt

with open('train_size_vs_f1.json', 'r') as f:
    results = json.load(f)

train_sizes = [r['train_size'] for r in results]
f1_scores = [r['average_f1_macro'] for r in results]

plt.figure(figsize=(8,5))
plt.plot(train_sizes, f1_scores, marker='o')
plt.xscale('log')
plt.xlabel('Training Set Size (log scale)')
plt.ylabel('Average F1 Macro')
plt.title('F1 Macro vs. Training Set Size')
plt.grid(True, which='both', ls='--', alpha=0.5)
plt.tight_layout()
plt.show()

TRAIN_SIZE = 25 
!python Cleanlab/select_cleanlab_medical.py \
  --preprocessed_data_dir="preprocessed_data/" \
  --output_file="Cleanlab/selected_train_ids_cleanlab.json" \
  --train_size={TRAIN_SIZE} \
  --random_seed=42 \
  --n_folds=3 \
  --class_balance="equal" \
  --extra_frac=0.5

!python eval_medical.py --preprocessed_data_dir preprocessed_data/ --selected_ids_json Cleanlab/selected_train_ids_cleanlab.json --metrics_output_file Cleanlab/results_selected_subset.json

pip install "numpy<2"

import json
import numpy as np

fixed_small_sizes = [25, 60, 100, 200, 500, 1000]


log_file = f'cleanlab_sweep_results.txt'

results = []
with open(log_file, 'w') as f:
    f.write("=" * 80 + "\n\n")
    
    for train_size in fixed_small_sizes:
        f.write("=" * 80 + "\n")
        
        # Run cleanlab selection
        !python Cleanlab/select_cleanlab_medical.py \
          --preprocessed_data_dir="preprocessed_data/" \
          --output_file="Cleanlab/selected_train_ids_cleanlab.json" \
          --train_size={train_size} \
          --random_seed=42 \
          --n_folds=3 \
          --class_balance="equal" \
          --extra_frac=0.5
    
        with open('Cleanlab/selected_train_ids_cleanlab.json', 'r') as rf:
            selected_data = json.load(rf)
            selected_ids = np.array(selected_data['selected_ids'])
            

        labels = np.load('preprocessed_data/y_train_full.npy')[selected_ids]
        unique, counts = np.unique(labels, return_counts=True)
        
        f.write(f"\nSelected training set created with {train_size} samples.\n")
        for label, count in zip(unique, counts):
            f.write(f"  Label {label}: {count} samples\n")
        
   
        !python eval_medical.py \
          --preprocessed_data_dir preprocessed_data/ \
          --selected_ids_json Cleanlab/selected_train_ids_cleanlab.json \
          --metrics_output_file results_selected_subset.json
        

        with open('results_selected_subset.json', 'r') as rf:
            metrics = json.load(rf)
        
        f.write("--- Evaluation Results ---\n")
        f.write(f"Individual F1 Macro Scores: {metrics.get('f1_macro_scores', metrics.get('individual_f1_scores', []))}\n")
        f.write(f"Average F1 Macro Score: {metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0)):.4f}\n")
        f.write(f"Standard Deviation of F1 Macro Scores: {metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0)):.4f}\n")
        
      
        f.write("\n\n")
        f.flush()  # Force write to disk

print(f'\nSweep complete! Detailed log saved to {log_file}')

import numpy as np
import matplotlib.pyplot as plt


train_sizes_orig = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_orig = [0.0999, 0.1000, 0.1792, 0.3471, 0.4952, 0.5189, 0.5087]
std_devs_orig = [0.0000, 0.0002, 0.0467, 0.0155, 0.0099, 0.0075, 0.0000]

# Cleanlab data (including full dataset)
train_sizes_cleanlab = [25, 60, 100, 200, 500, 1000, 11550]
f1_scores_cleanlab = [0.1569, 0.3716, 0.5255, 0.5683, 0.5887, 0.6046, 0.5087]
std_devs_cleanlab = [0.0924, 0.1302, 0.0210, 0.0046, 0.0010, 0.0011, 0.0000]

plt.figure(figsize=(12, 8))


plt.errorbar(train_sizes_orig, f1_scores_orig, yerr=std_devs_orig, 
             fmt='o-', capsize=5, label='Random Selection', color='blue')

# Plot cleanlab data
plt.errorbar(train_sizes_cleanlab, f1_scores_cleanlab, yerr=std_devs_cleanlab, 
             fmt='s-', capsize=5, label='Cleanlab Selection', color='red')

plt.xscale('log')
plt.grid(True, which="both", ls="-", alpha=0.2)

plt.xlabel('Training Set Size (log scale)')
plt.ylabel('F1 Macro Score')
plt.title('F1 Macro Score vs Training Size: Random vs Cleanlab Selection')
plt.legend()


for x, y in zip(train_sizes_orig, f1_scores_orig):
    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                xytext=(0,10), ha='center', color='blue')
for x, y in zip(train_sizes_cleanlab, f1_scores_cleanlab):
    plt.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                xytext=(0,-15), ha='center', color='red')

plt.tight_layout()
plt.show()

# Load and display duplicate report
with open('Cleanlab/duplicate_report.txt', 'r') as f:
    print(f.read())

# Install required dependencies
!pip install nltk scikit-learn pandas numpy tqdm joblib

!python preprocess_medical.py --input_dir="." --output_dir="preprocessed_data/"

# Step 2: Run cleanlab selection with duplicate detection
TRAIN_SIZE = 25
!python Cleanlab/select_cleanlab_medical.py \
  --preprocessed_data_dir="preprocessed_data/" \
  --output_file="Cleanlab/selected_train_ids_cleanlab.json" \
  --train_size={TRAIN_SIZE} \
  --random_seed=42 \
  --n_folds=3 \
  --class_balance="equal" \
  --extra_frac=0.5

# Step 3: Inspect the duplicate detection results
try:
    with open('Cleanlab/duplicate_report.txt', 'r') as f:
        print(f.read())
except FileNotFoundError:
    print("No duplicate report found. Make sure the cleanlab selection completed successfully.")