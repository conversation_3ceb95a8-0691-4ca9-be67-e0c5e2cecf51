import numpy as np
import json
import os
import fire
from scipy.sparse import csr_matrix
from sklearn.neighbors import NearestNeighbors
from sklearn_extra.cluster import KMedoids

# Define a TrainingSet structure 
class TrainingSet:
    def __init__(self, samples_by_class: dict):
        self.samples_by_class = samples_by_class

    def to_json(self, filename: str):
        # Output as {'selected_ids': [...]} for compatibility
        all_ids = []
        for ids_list in self.samples_by_class.values():
            all_ids.extend(ids_list)
        data_to_save = {'selected_ids': all_ids}
        with open(filename, 'w') as f:
            json.dump(data_to_save, f, indent=4)

    def total_selected(self):
        return sum(len(ids) for ids in self.samples_by_class.values())

    def all_selected_ids(self):
        all_ids = set()
        for ids_list in self.samples_by_class.values():
            all_ids.update(ids_list)
        return all_ids

def load_sparse_csr(filename):
    loader = np.load(filename)
    return csr_matrix((loader['data'], loader['indices'], loader['indptr']), shape=loader['shape'])

class InterClassSeparation:
    def __init__(
        self,
        train_x_path: str,
        train_y_path: str,
        train_ids_path: str,
        train_set_size_limit: int,
        random_seed: int = 42,
    ):
        self.train_x_path = train_x_path
        self.train_y_path = train_y_path
        self.train_ids_path = train_ids_path
        self.train_set_size_limit = train_set_size_limit
        self.random_seed = random_seed

        print("Loading preprocessed training data for selection...")
        self.X_train_full_vec = load_sparse_csr(train_x_path)
        self.y_train_full = np.load(train_y_path)
        self.train_sample_ids = np.load(train_ids_path)
        self.unique_labels = np.unique(self.y_train_full)
        print(f"Full training data contains {len(self.y_train_full)} samples across {len(self.unique_labels)} classes.")

    def select(self, k=10, m=5) -> TrainingSet:
        """
        Chọn các điểm dữ liệu mà trong k láng giềng gần nhất, có ít hơn m điểm khác class, sau đó KMedoids mỗi class.
        Args:
            k: Số láng giềng gần nhất để xét
            m: Ngưỡng số lượng điểm khác class để loại
        Returns:
            TrainingSet: Object chứa các sample ID được chọn, nhóm theo class
        """
        X = self.X_train_full_vec
        y = self.y_train_full
        ids = self.train_sample_ids
        unique_labels = self.unique_labels
        n_class = len(unique_labels)
        n_medoids = max(1, self.train_set_size_limit // n_class)

        # Fit NearestNeighbors (bỏ chính nó ra khỏi láng giềng)
        #nn = NearestNeighbors(n_neighbors=k+1, metric='cosine')
        #nn.fit(X)
        #distances, indices = nn.kneighbors(X)

        # indices: [n_samples, k+1], indices[:,0] là chính nó
        selected_mask = np.ones(len(y), dtype=bool)
        #for i in range(len(y)):
        #    neighbor_idx = indices[i, 1:]  # Bỏ chính nó
        #    neighbor_labels = y[neighbor_idx]
        #    n_diff = np.sum(neighbor_labels != y[i])
        #    if n_diff >= m:
        #        selected_mask[i] = False

        # Chỉ giữ lại các điểm được chọn
        X_selected = X[selected_mask]
        y_selected = y[selected_mask]
        ids_selected = ids[selected_mask]

        selected_samples_by_class = {int(label): [] for label in unique_labels}
        for label in unique_labels:
            idx_in_class = np.where(y_selected == label)[0]
            if len(idx_in_class) == 0:
                continue
            X_class = X_selected[idx_in_class]
            ids_class = ids_selected[idx_in_class]
            n_medoid = min(n_medoids, len(idx_in_class))
            if n_medoid == 0:
                continue
            # KMedoids không hỗ trợ sparse, cần chuyển sang dense
            if hasattr(X_class, 'toarray'):
                X_class_dense = X_class.toarray()
            else:
                X_class_dense = X_class
            try:
                kmedoids = KMedoids(n_clusters=n_medoid, init="k-medoids++", random_state=self.random_seed)
                kmedoids.fit(X_class_dense)
                medoid_indices = kmedoids.medoid_indices_
                selected_ids = ids_class[medoid_indices]
                selected_samples_by_class[int(label)] = [int(i) for i in selected_ids]
            except Exception as e:
                print(f"KMedoids failed for class {label}: {e}")
                # Nếu lỗi, chọn ngẫu nhiên n_medoid điểm
                if len(idx_in_class) >= n_medoid:
                    fallback_idx = np.random.choice(idx_in_class, n_medoid, replace=False)
                    selected_ids = ids_class[fallback_idx]
                    selected_samples_by_class[int(label)] = [int(i) for i in selected_ids]

        return TrainingSet(samples_by_class=selected_samples_by_class)

def main(
    preprocessed_data_dir: str = "preprocessed_data/",
    output_file: str = "Inter-classSeparation//selected_train_ids_inter-class.json",
    train_size: int = 100,
    random_seed: int = 42,
):
    """
    Runs the new selection method (template).
    Args:
        preprocessed_data_dir: Directory containing the preprocessed .npz and .npy files.
        output_file: Path to save the selected sample IDs (JSON format).
        train_size: The total number of samples to select.
        random_seed: Random seed for selection.
    """
    train_x_path = os.path.join(preprocessed_data_dir, 'X_train_full_vec.npz')
    train_y_path = os.path.join(preprocessed_data_dir, 'y_train_full.npy')
    train_ids_path = os.path.join(preprocessed_data_dir, 'train_sample_ids.npy')
    selector = InterClassSeparation(
        train_x_path=train_x_path,
        train_y_path=train_y_path,
        train_ids_path=train_ids_path,
        train_set_size_limit=train_size,
        random_seed=random_seed,
    )
    selected_set = selector.select()
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    selected_set.to_json(output_file)
    print(f"\nSelection script finished. Selected {len(list(selected_set.all_selected_ids()))} samples.")
    print(f"Selected IDs saved to {output_file}")

if __name__ == "__main__":
    fire.Fire(main)
