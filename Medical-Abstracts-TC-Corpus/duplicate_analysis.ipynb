# Cell 1: Import and setup
import pandas as pd
import numpy as np
import sklearn.feature_extraction.text
import sklearn.linear_model
from collections import defaultdict, Counter
import scipy.sparse
import cleanlab
from cleanlab.classification import CleanLearning
import joblib
import os
import re
import string
from typing import Dict, List, Tuple, Set


# Cell 2: Define the DuplicateAnalyzer class
class DuplicateAnalyzer:
    def __init__(self, input_dir: str = "", train_file: str = "medical_tc_train.csv"):
        self.input_dir = input_dir
        self.train_file = train_file
        self.df = None
        self.cleaned_texts = None
        self.vectorizer = None
        self.X_vec = None
        self.duplicate_groups = None
        self.cleanlab_issues = None
        
    def clean_text(self, text):
        """Clean text using the same method as preprocess_medical.py"""
        if not isinstance(text, str):
            return ""
        text = text.lower()
        text = re.sub(r'\@\w+|\#', '', text)
        text = re.sub(r'[%s]' % re.escape(string.punctuation), '', text)
        text = re.sub(r'\d+', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def load_and_preprocess_data(self):
        """Load and preprocess the medical data"""
        print("Loading and preprocessing data...")
        
        # Load data
        train_csv_path = os.path.join(self.input_dir, self.train_file)
        self.df = pd.read_csv(train_csv_path)
        print(f"Loaded {len(self.df)} samples")
        
        # Clean texts
        self.cleaned_texts = [self.clean_text(text) for text in self.df["medical_abstract"].values]
        
        # Create TF-IDF vectors
        self.vectorizer = sklearn.feature_extraction.text.TfidfVectorizer(
            max_features=10000,
            stop_words='english'
        )
        self.X_vec = self.vectorizer.fit_transform(self.cleaned_texts)
        print(f"Vectorization complete. Shape: {self.X_vec.shape}")
    
    def find_exact_text_duplicates(self):
        """Find groups of samples with identical cleaned text"""
        print("Finding exact text duplicates...")
        
        text_to_indices = defaultdict(list)
        for idx, text in enumerate(self.cleaned_texts):
            if text.strip():
                text_to_indices[text].append(idx)
        
        duplicate_groups = {text: indices for text, indices in text_to_indices.items() 
                          if len(indices) > 1}
        
        self.duplicate_groups = duplicate_groups
        
        print(f"Found {len(duplicate_groups)} unique texts with duplicates")
        total_duplicate_samples = sum(len(indices) for indices in duplicate_groups.values())
        print(f"Total samples involved in duplicates: {total_duplicate_samples}")
        
        return duplicate_groups
    
    def analyze_duplicate_labels(self):
        """Analyze label distribution within duplicate groups"""
        if self.duplicate_groups is None:
            self.find_exact_text_duplicates()
        
        print("Analyzing label distribution in duplicate groups...")
        
        analysis = {
            'conflicting_groups': [],
            'consistent_groups': [],
            'conflicting_indices': set(),
            'total_conflicting_samples': 0
        }
        
        for text, indices in self.duplicate_groups.items():
            labels = [self.df.iloc[idx]['condition_label'] for idx in indices]
            unique_labels = set(labels)
            
            group_info = {
                'text_preview': text[:100] + "..." if len(text) > 100 else text,
                'indices': indices,
                'labels': labels,
                'unique_labels': list(unique_labels),
                'sample_count': len(indices)
            }
            
            if len(unique_labels) > 1:
                analysis['conflicting_groups'].append(group_info)
                analysis['conflicting_indices'].update(indices)
            else:
                analysis['consistent_groups'].append(group_info)
        
        analysis['total_conflicting_samples'] = len(analysis['conflicting_indices'])
        
        print(f"Groups with conflicting labels: {len(analysis['conflicting_groups'])}")
        print(f"Groups with consistent labels: {len(analysis['consistent_groups'])}")
        print(f"Total samples with conflicting duplicate labels: {analysis['total_conflicting_samples']}")
        
        return analysis
    
    def run_cleanlab_analysis(self, num_folds: int = 5):
        """Run cleanlab to find label issues"""
        print(f"Running cleanlab analysis with {num_folds} folds...")
        
        # Prepare data
        y_original = self.df["condition_label"].values
        unique_y = np.unique(y_original)
        label_map = {old: new for new, old in enumerate(unique_y)}
        y_zero_indexed = np.array([label_map[y] for y in y_original])
        
        # Initialize cleanlab
        clf = sklearn.linear_model.LogisticRegression(solver='liblinear', random_state=42)
        cl = CleanLearning(clf, seed=42, verbose=True, cv_n_folds=num_folds)
        
        # Find label issues
        issues = cl.find_label_issues(self.X_vec, y_zero_indexed)
        issues['original_index'] = issues.index
        issues['original_label'] = y_original
        issues['text_preview'] = [text[:100] + "..." if len(text) > 100 else text 
                                for text in self.cleaned_texts]
        
        self.cleanlab_issues = issues
        
        print(f"Cleanlab found {issues['is_label_issue'].sum()} label issues")
        print(f"Mean label quality score: {issues['label_quality'].mean():.4f}")
        
        return issues
    
    def compare_cleanlab_vs_duplicates(self, duplicate_analysis, cleanlab_issues):
        """Compare cleanlab findings with true duplicate analysis"""
        print("Comparing cleanlab results with true duplicate analysis...")
        
        cleanlab_flagged = set(cleanlab_issues[cleanlab_issues['is_label_issue']].index.tolist())
        true_conflicts = duplicate_analysis['conflicting_indices']
        
        overlap = cleanlab_flagged.intersection(true_conflicts)
        cleanlab_only = cleanlab_flagged - true_conflicts
        duplicates_only = true_conflicts - cleanlab_flagged
        
        comparison = {
            'cleanlab_flagged_count': len(cleanlab_flagged),
            'true_conflicts_count': len(true_conflicts),
            'overlap_count': len(overlap),
            'cleanlab_only_count': len(cleanlab_only),
            'duplicates_only_count': len(duplicates_only),
            'overlap_indices': overlap,
            'cleanlab_only_indices': cleanlab_only,
            'duplicates_only_indices': duplicates_only
        }
        
        # Calculate metrics
        if len(true_conflicts) > 0:
            comparison['recall'] = len(overlap) / len(true_conflicts)
        else:
            comparison['recall'] = None
            
        if len(cleanlab_flagged) > 0:
            comparison['precision'] = len(overlap) / len(cleanlab_flagged)
        else:
            comparison['precision'] = None
        
        print(f"Cleanlab flagged: {len(cleanlab_flagged)} samples")
        print(f"True conflicting duplicates: {len(true_conflicts)} samples")
        print(f"Overlap: {len(overlap)} samples")
        print(f"Cleanlab-only flags: {len(cleanlab_only)} samples")
        print(f"Missed true conflicts: {len(duplicates_only)} samples")
        
        if comparison['recall'] is not None:
            print(f"Recall: {comparison['recall']:.2%}")
        if comparison['precision'] is not None:
            print(f"Precision: {comparison['precision']:.2%}")
        
        return comparison


# Cell 3: Run the analysis
# Initialize analyzer - UPDATE THESE PATHS
analyzer = DuplicateAnalyzer(
    input_dir=".",  # Current directory, or specify full path
    train_file="medical_tc_train.csv"  # Your CSV file name
)

# Load and preprocess data
analyzer.load_and_preprocess_data()

# Cell 4: Find duplicates
duplicate_groups = analyzer.find_exact_text_duplicates()
duplicate_analysis = analyzer.analyze_duplicate_labels()

# Cell 5: Run cleanlab
cleanlab_issues = analyzer.run_cleanlab_analysis(num_folds=3)

# Cell 6: Compare results
comparison = analyzer.compare_cleanlab_vs_duplicates(duplicate_analysis, cleanlab_issues)


# Cell 7: Show detailed results
print("\n" + "="*60)
print("SUMMARY RESULTS")
print("="*60)
print(f"Total samples: {len(analyzer.df)}")
print(f"Duplicate groups: {len(duplicate_groups)}")
print(f"Conflicting duplicate samples: {duplicate_analysis['total_conflicting_samples']}")
print(f"Cleanlab flagged samples: {comparison['cleanlab_flagged_count']}")
print(f"Overlap (cleanlab caught duplicates): {comparison['overlap_count']}")

if comparison['recall'] is not None:
    print(f"\nRecall (% of duplicates caught by cleanlab): {comparison['recall']:.1%}")
if comparison['precision'] is not None:
    print(f"Precision (% of cleanlab flags that are duplicates): {comparison['precision']:.1%}")



# Cell 8: Show examples of conflicting duplicates
print(f"\nEXAMPLES OF CONFLICTING DUPLICATES:")
for i, group in enumerate(duplicate_analysis['conflicting_groups'][:3]):
    print(f"\nExample {i+1}:")
    print(f"Indices: {group['indices']}")
    print(f"Labels: {group['labels']}")
    print(f"Text: {group['text_preview']}")
    
    caught = [idx for idx in group['indices'] if idx in comparison['overlap_indices']]
    print(f"Caught by cleanlab: {len(caught)}/{len(group['indices'])} samples")



# Cell 9: Show examples missed by cleanlab
if len(comparison['duplicates_only_indices']) > 0:
    print(f"\nEXAMPLES MISSED BY CLEANLAB:")
    missed_sample = list(comparison['duplicates_only_indices'])[:3]
    for idx in missed_sample:
        print(f"\nIndex {idx}:")
        print(f"Label: {analyzer.df.iloc[idx]['condition_label']}")
        print(f"Text: {analyzer.cleaned_texts[idx][:100]}...")

