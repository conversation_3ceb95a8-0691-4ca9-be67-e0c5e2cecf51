name: <PERSON><PERSON><PERSON><PERSON> DataPerf
description: <PERSON><PERSON>ommons DataPerf integration
authors:
  - { name: "MLCommons Best Practices Working Group" }

platform:
  accelerator_count: 0

docker:
  # Image name.
  image: mlcommons/dataperf:0.0.1
  # Docker build context relative to $MLCUBE_ROOT. Default is `build`.
  build_context: "."
  # Docker file name within docker build context, default is `Dockerfile`.
  build_file: "Dockerfile_mlcube"

tasks:
  download:
    # Download dataset
    parameters:
      inputs: { parameters_file: { type: file, default: parameters.yaml } }
      outputs: { output_path: data/ }

  select:
    # Run selection algorithm
    parameters:
      inputs:
        {
          config_file: { type: file, default: dataperf_speech_config.yaml },
          allowed_training_set: { type: file, default: data/dataperf_en_data/allowed_training_set.yaml },
          train_embeddings_dir: data/dataperf_en_data/train_embeddings/,
        }
      outputs: { outdir: select_output/ }

  evaluate:
    # Perfom evaluation
    parameters:
      inputs:
        {
          eval_embeddings_dir: data/dataperf_en_data/eval_embeddings/,
          train_embeddings_dir: data/dataperf_en_data/train_embeddings/,
          allowed_training_set: { type: file, default: data/dataperf_en_data/allowed_training_set.yaml },
          eval_file: { type: file, default: data/dataperf_en_data/eval.yaml },
          train_file: { type: file, default: select_output/en_train.json },
          config_file: { type: file, default: dataperf_speech_config.yaml },
        }
      outputs: { log_path: { type: file, default: log.txt } }
