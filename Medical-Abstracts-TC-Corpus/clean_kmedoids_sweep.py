#!/usr/bin/env python3
"""
Sweep evaluation for clean K-medoids selection approach
"""

import json
import numpy as np
import subprocess
import os
import time

def run_command(command):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=True)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def main():
    # Configuration
    fixed_small_sizes = [25, 60, 100, 200, 500, 1000]
    preprocessed_data_dir = "preprocessed_data_clean/"
    log_file = 'clean_kmedoids_sweep_results.txt'
    
    print("="*80)
    print("🧹 CLEAN K-MEDOIDS SWEEP EVALUATION")
    print("="*80)
    
    # First, run preprocessing if needed
    if not os.path.exists(preprocessed_data_dir):
        print("🔧 Preprocessing clean dataset...")
        success, stdout, stderr = run_command(
            "python clean_preprocess_medical.py "
            "--input_dir='' "
            "--output_dir=preprocessed_data_clean/ "
            "--train_file=medical_train.csv "
            "--test_file=medical_test.csv "
            "--max_features=10000"
        )
        
        if not success:
            print(f"❌ Preprocessing failed!")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return
        else:
            print("✅ Preprocessing completed successfully")
    else:
        print(f"✅ Using existing preprocessed data in {preprocessed_data_dir}")
    
    # Create CleanKmedoids directory
    os.makedirs("CleanKmedoids", exist_ok=True)
    
    results = []
    
    with open(log_file, 'w') as f:
        f.write("="*80 + "\n")
        f.write("CLEAN K-MEDOIDS SWEEP EVALUATION RESULTS\n")
        f.write("="*80 + "\n\n")
        f.write(f"Evaluation started at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Dataset: Clean medical abstracts (duplicates removed)\n")
        f.write(f"Method: K-medoids clustering with equal class balance\n")
        f.write(f"Sizes evaluated: {fixed_small_sizes}\n\n")
        
        for train_size in fixed_small_sizes:
            f.write("="*80 + "\n")
            f.write(f"EVALUATING TRAIN SIZE: {train_size}\n")
            f.write("="*80 + "\n")
            
            print(f"\n🎯 Evaluating train size: {train_size}")
            
            # Run clean K-medoids selection
            print("  🔄 Running K-medoids selection...")
            selection_command = (
                f"python CleanKmedoids/select_clean_kmedoids.py "
                f"--preprocessed_data_dir={preprocessed_data_dir} "
                f"--output_file=CleanKmedoids/selected_train_ids_clean_kmedoids.json "
                f"--train_size={train_size} "
                f"--random_seed=42 "
                f"--class_balance=equal"
            )
            
            success, stdout, stderr = run_command(selection_command)
            
            if not success:
                error_msg = f"❌ Selection failed for size {train_size}"
                print(error_msg)
                f.write(f"{error_msg}\n")
                f.write(f"STDOUT: {stdout}\n")
                f.write(f"STDERR: {stderr}\n\n")
                continue
            
            f.write("Selection Output:\n")
            f.write(stdout + "\n")
            
            # Load and display selected IDs info
            try:
                with open('CleanKmedoids/selected_train_ids_clean_kmedoids.json', 'r') as rf:
                    selected_data = json.load(rf)
                    selected_ids = np.array(selected_data['selected_ids'])
                
                # Load labels to check distribution
                labels = np.load(f'{preprocessed_data_dir}/y_train_full.npy')[selected_ids]
                unique, counts = np.unique(labels, return_counts=True)
                
                f.write(f"\nSelected training set created with {train_size} samples.\n")
                for label, count in zip(unique, counts):
                    f.write(f"  Label {label}: {count} samples\n")
                
                print(f"  ✅ Selected {len(selected_ids)} samples")
                
            except Exception as e:
                error_msg = f"❌ Error loading selection results: {e}"
                print(error_msg)
                f.write(f"{error_msg}\n\n")
                continue
            
            # Run evaluation
            print("  🔄 Running evaluation...")
            eval_command = (
                f"python eval_medical.py "
                f"--preprocessed_data_dir {preprocessed_data_dir} "
                f"--selected_ids_json CleanKmedoids/selected_train_ids_clean_kmedoids.json "
                f"--metrics_output_file results_selected_subset.json"
            )
            
            success, stdout, stderr = run_command(eval_command)
            
            if not success:
                error_msg = f"❌ Evaluation failed for size {train_size}"
                print(error_msg)
                f.write(f"{error_msg}\n")
                f.write(f"STDOUT: {stdout}\n")
                f.write(f"STDERR: {stderr}\n\n")
                continue
            
            f.write("Evaluation Output:\n")
            f.write(stdout + "\n")
            
            # Load and display metrics
            try:
                with open('results_selected_subset.json', 'r') as rf:
                    metrics = json.load(rf)
                
                f.write("--- Evaluation Results ---\n")
                f.write(f"Individual F1 Macro Scores: {metrics.get('individual_scores', metrics.get('f1_macro_scores', []))}\n")
                f.write(f"Average F1 Macro Score: {metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0)):.4f}\n")
                f.write(f"Standard Deviation of F1 Macro Scores: {metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0)):.4f}\n")
                
                # Store results for summary
                results.append({
                    'train_size': train_size,
                    'avg_f1': metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0)),
                    'std_f1': metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0)),
                    'individual_scores': metrics.get('individual_scores', metrics.get('f1_macro_scores', []))
                })
                
                print(f"  ✅ F1 Score: {metrics.get('average_f1_macro', 0.0):.4f} ± {metrics.get('std_f1_macro', 0.0):.4f}")
                
            except Exception as e:
                error_msg = f"❌ Error loading evaluation results: {e}"
                print(error_msg)
                f.write(f"{error_msg}\n")
            
            f.write("\n\n")
            f.flush()  # Force write to disk
        
        # Write summary
        f.write("="*80 + "\n")
        f.write("SUMMARY OF ALL RESULTS\n")
        f.write("="*80 + "\n")
        
        if results:
            f.write("Train Size | Avg F1 Score | Std F1 Score\n")
            f.write("-"*40 + "\n")
            for result in results:
                f.write(f"{result['train_size']:9d} | {result['avg_f1']:11.4f} | {result['std_f1']:11.4f}\n")
            
            # Find best result
            best_result = max(results, key=lambda x: x['avg_f1'])
            f.write(f"\nBest performance: {best_result['avg_f1']:.4f} ± {best_result['std_f1']:.4f} at size {best_result['train_size']}\n")
        else:
            f.write("No successful evaluations completed.\n")
        
        f.write(f"\nEvaluation completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print(f'\n✅ Sweep complete! Detailed log saved to {log_file}')
    
    # Display summary
    if results:
        print("\n📊 SUMMARY:")
        print("Train Size | Avg F1 Score | Std F1 Score")
        print("-"*40)
        for result in results:
            print(f"{result['train_size']:9d} | {result['avg_f1']:11.4f} | {result['std_f1']:11.4f}")
        
        best_result = max(results, key=lambda x: x['avg_f1'])
        print(f"\n🏆 Best: {best_result['avg_f1']:.4f} ± {best_result['std_f1']:.4f} at size {best_result['train_size']}")

if __name__ == "__main__":
    main()
