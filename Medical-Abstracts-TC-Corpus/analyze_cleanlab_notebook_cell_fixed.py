# Add this as a new cell in your medical.ipynb notebook

# ============================================================================
# ANALYSIS: What did cleanlab actually identify vs. true duplicates?
# ============================================================================

import numpy as np
import pandas as pd
import scipy.sparse
from cleanlab.classification import CleanLearning
import sklearn.linear_model

def load_data_matrix(file_path):
    """Load matrix from .npz file - handles both sparse and dense matrices"""
    try:
        # First try loading as sparse matrix
        return scipy.sparse.load_npz(file_path)
    except ValueError:
        # If that fails, try loading as regular numpy array
        data = np.load(file_path)
        if hasattr(data, 'files'):
            # It's an npz archive, get the first array
            key = data.files[0]
            matrix = data[key]
            data.close()
            return matrix
        else:
            return data

print("="*80)
print("ANALYZING CLEANLAB RESULTS vs TRUE DUPLICATES")
print("="*80)

# Load the data
print("Loading data...")
X_train_full_vec = load_data_matrix("preprocessed_data/X_train_full_vec.npz")
y_train_full = np.load("preprocessed_data/y_train_full.npy")
train_sample_ids = np.load("preprocessed_data/train_sample_ids.npy")

print(f"Loaded data: {X_train_full_vec.shape[0]} samples, {X_train_full_vec.shape[1]} features")
print(f"Data type: {type(X_train_full_vec)}")
print(f"Classes: {np.unique(y_train_full)}")

# Convert to sparse if it's dense (for memory efficiency)
if not scipy.sparse.issparse(X_train_full_vec):
    print("Converting dense matrix to sparse for memory efficiency...")
    X_train_full_vec = scipy.sparse.csr_matrix(X_train_full_vec)

# Load abstracts
try:
    with open('preprocessed_data/train_abstracts.txt', 'r', encoding='utf-8') as f:
        abstracts = [line.strip() for line in f.readlines()]
    print(f"Loaded {len(abstracts)} abstracts")
except Exception as e:
    print(f"Could not load abstracts: {e}")
    abstracts = None

print("\n" + "="*60)
print("ANALYSIS 1: Finding true duplicates (identical feature vectors)")
print("="*60)

# Find true duplicates using feature vectors
duplicate_pairs = []
feature_vector_map = {}

print("Scanning for duplicate feature vectors...")
for idx in range(min(1000, X_train_full_vec.shape[0])):  # Limit to first 1000 for speed
    if idx % 100 == 0:
        print(f"  Processed {idx} samples...")
    
    if scipy.sparse.issparse(X_train_full_vec):
        feature_vector = X_train_full_vec[idx].toarray().tobytes()
    else:
        feature_vector = X_train_full_vec[idx].tobytes()
    
    label = y_train_full[idx]
    
    if feature_vector in feature_vector_map:
        prev_idx, prev_label = feature_vector_map[feature_vector]
        if prev_label != label:
            duplicate_pairs.append({
                'idx1': prev_idx,
                'id1': train_sample_ids[prev_idx],
                'label1': prev_label,
                'idx2': idx,
                'id2': train_sample_ids[idx],
                'label2': label,
                'text1': abstracts[prev_idx] if abstracts else "Text not available",
                'text2': abstracts[idx] if abstracts else "Text not available"
            })
    else:
        feature_vector_map[feature_vector] = (idx, label)

print(f"Found {len(duplicate_pairs)} true duplicate pairs with different labels")

if duplicate_pairs:
    print("\nTrue duplicate pairs:")
    for i, pair in enumerate(duplicate_pairs[:3]):  # Show first 3
        print(f"\nPair {i+1}:")
        print(f"  Sample 1: ID {pair['id1']}, Label {pair['label1']}")
        print(f"  Sample 2: ID {pair['id2']}, Label {pair['label2']}")
        if abstracts:
            print(f"  Texts identical: {pair['text1'] == pair['text2']}")
            print(f"  Text: {pair['text1'][:200]}...")
else:
    print("No true duplicates found in the first 1000 samples!")

print("\n" + "="*60)
print("ANALYSIS 2: Checking feature vector groups")
print("="*60)

# Find all samples with identical feature vectors (regardless of labels)
feature_vector_groups = {}
print("Building feature vector groups...")
for idx in range(min(1000, X_train_full_vec.shape[0])):  # Limit for speed
    if scipy.sparse.issparse(X_train_full_vec):
        feature_vector = X_train_full_vec[idx].toarray().tobytes()
    else:
        feature_vector = X_train_full_vec[idx].tobytes()
    
    if feature_vector not in feature_vector_groups:
        feature_vector_groups[feature_vector] = []
    feature_vector_groups[feature_vector].append(idx)

# Find groups with multiple samples
multi_sample_groups = {k: v for k, v in feature_vector_groups.items() if len(v) > 1}
print(f"Found {len(multi_sample_groups)} feature vector groups with multiple samples")

if multi_sample_groups and abstracts:
    print("\nAnalyzing first few groups:")
    for i, (fv, indices) in enumerate(list(multi_sample_groups.items())[:3]):  # Show first 3
        print(f"\nGroup {i+1} ({len(indices)} samples):")
        texts = [abstracts[idx] for idx in indices]
        labels = [y_train_full[idx] for idx in indices]
        ids = [train_sample_ids[idx] for idx in indices]
        
        for j, (idx, text, label, sample_id) in enumerate(zip(indices, texts, labels, ids)):
            print(f"  Sample {j+1}: ID {sample_id}, Label {label}")
            print(f"    Text: {text[:100]}...")
        
        # Check if all texts are identical
        all_identical = all(text == texts[0] for text in texts)
        print(f"  All texts identical: {all_identical}")
        
        # Check if labels are different
        unique_labels = set(labels)
        print(f"  Unique labels: {unique_labels}")
        if len(unique_labels) > 1:
            print(f"  *** This group has different labels for same feature vector! ***")

print("\n" + "="*60)
print("ANALYSIS 3: Understanding what cleanlab identifies")
print("="*60)

# Run cleanlab to see what it identifies (on a subset for speed)
subset_size = min(1000, X_train_full_vec.shape[0])
X_subset = X_train_full_vec[:subset_size]
y_subset = y_train_full[:subset_size]

clf = sklearn.linear_model.LogisticRegression(solver='liblinear', random_state=42)
cl = CleanLearning(clf, seed=42, verbose=False, cv_n_folds=3)

# Convert labels to 0-indexed
unique_y = np.unique(y_subset)
label_map = {old: new for new, old in enumerate(unique_y)}
y_zero_indexed = np.array([label_map[y] for y in y_subset])

print(f"Running cleanlab analysis on {subset_size} samples...")
issues = cl.find_label_issues(X_subset, y_zero_indexed)

cleanlab_issues = issues[issues['is_label_issue']]
print(f"Cleanlab identified {len(cleanlab_issues)} samples with label issues")

if len(cleanlab_issues) > 0 and abstracts:
    print(f"\nFirst 3 cleanlab-identified issues:")
    for i, idx in enumerate(cleanlab_issues.index[:3]):
        label = y_subset[idx]
        sample_id = train_sample_ids[idx]
        quality_score = issues.loc[idx, 'label_quality']
        text = abstracts[idx]
        print(f"\nIssue {i+1}:")
        print(f"  Sample ID: {sample_id}, Label: {label}")
        print(f"  Quality Score: {quality_score:.3f}")
        print(f"  Text: {text[:200]}...")

print("\n" + "="*60)
print("SUMMARY")
print("="*60)
print(f"True duplicates (same feature vector, different labels): {len(duplicate_pairs)}")
print(f"Feature vector groups with multiple samples: {len(multi_sample_groups)}")
print(f"Cleanlab-identified label issues: {len(cleanlab_issues)}")

# Check overlap
if duplicate_pairs:
    duplicate_indices = set()
    for pair in duplicate_pairs:
        duplicate_indices.add(pair['idx1'])
        duplicate_indices.add(pair['idx2'])
    
    cleanlab_indices = set(cleanlab_issues.index)
    overlap = duplicate_indices.intersection(cleanlab_indices)
    print(f"Overlap between true duplicates and cleanlab issues: {len(overlap)}")

print("\nCONCLUSION:")
print("- Cleanlab identifies samples that appear mislabeled based on model predictions")
print("- This is different from finding identical feature vectors with different labels")
print("- The current 'duplicate_issues.txt' file contains cleanlab issues, not true duplicates")
print("- True duplicates would have identical text but different labels")

# Save results for further analysis
if duplicate_pairs:
    print(f"\nSaving {len(duplicate_pairs)} true duplicate pairs to 'true_duplicates_analysis.txt'")
    with open('true_duplicates_analysis.txt', 'w') as f:
        f.write("TRUE DUPLICATE PAIRS ANALYSIS\n")
        f.write("="*50 + "\n\n")
        for i, pair in enumerate(duplicate_pairs):
            f.write(f"Pair {i+1}:\n")
            f.write(f"  Sample 1: ID {pair['id1']}, Label {pair['label1']}\n")
            f.write(f"  Sample 2: ID {pair['id2']}, Label {pair['label2']}\n")
            f.write(f"  Texts identical: {pair['text1'] == pair['text2']}\n")
            f.write(f"  Text 1: {pair['text1']}\n")
            f.write(f"  Text 2: {pair['text2']}\n")
            f.write("\n" + "-"*50 + "\n\n")

print("\nAnalysis complete!")
print("Note: Analysis was limited to first 1000 samples for performance.")
print("If you want to analyze the full dataset, remove the 'min(1000, ...)' limits.")
