# Cell 1: Import and setup (FIXED VERSION)
import pandas as pd
import numpy as np
import sklearn.feature_extraction.text
from sklearn_extra.cluster import KMedoids
from collections import defaultdict, Counter
import scipy.sparse
import json
import os
import re
import string
import time
import argparse
from typing import Dict, List, Tuple, Set

class DuplicateRemover:
    """Remove samples with true duplicate label conflicts"""
    
    def __init__(self):
        self.duplicate_groups = None
        self.conflicting_indices = set()
    
    def clean_text(self, text):
        """Clean text using the same method as preprocess_medical.py"""
        if not isinstance(text, str):
            return ""
        text = text.lower()
        text = re.sub(r'\@\w+|\#', '', text)
        text = re.sub(r'[%s]' % re.escape(string.punctuation), '', text)
        text = re.sub(r'\d+', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def find_conflicting_duplicates(self, texts, labels):
        """Find indices of samples that have conflicting labels for identical text"""
        print("Finding conflicting duplicates...")
        
        # Clean texts
        cleaned_texts = [self.clean_text(text) for text in texts]
        
        # Group by cleaned text
        text_to_indices = defaultdict(list)
        for idx, text in enumerate(cleaned_texts):
            if text.strip():
                text_to_indices[text].append(idx)
        
        # Find groups with conflicting labels
        conflicting_indices = set()
        conflicting_groups = []
        
        for text, indices in text_to_indices.items():
            if len(indices) > 1:
                group_labels = [labels[idx] for idx in indices]
                unique_labels = set(group_labels)
                
                if len(unique_labels) > 1:
                    # This group has conflicting labels
                    conflicting_indices.update(indices)
                    conflicting_groups.append({
                        'text': text[:100] + "..." if len(text) > 100 else text,
                        'indices': indices,
                        'labels': group_labels
                    })
        
        self.conflicting_indices = conflicting_indices
        self.duplicate_groups = conflicting_groups
        
        print(f"Found {len(conflicting_groups)} groups with conflicting labels")
        print(f"Total conflicting samples to remove: {len(conflicting_indices)}")
        
        return conflicting_indices
    
    def detect_duplicates(self, texts, labels):
        """Alias for find_conflicting_duplicates for compatibility"""
        return self.find_conflicting_duplicates(texts, labels)

class KMedoidsSelector:
    """K-Medoids based sample selection with duplicate removal"""
    
    def __init__(self, random_seed=42):
        self.random_seed = random_seed
        self.vectorizer = None
        self.duplicate_remover = DuplicateRemover()
    
    def load_data(self, preprocessed_data_dir, input_dir="", train_file="medical_tc_train.csv"):
        """Load preprocessed data"""
        print("Loading preprocessed data...")
        
        # Load sparse matrix from .npz file
        X_train_npz = np.load(os.path.join(preprocessed_data_dir, 'X_train_full_vec.npz'))
        X_train = scipy.sparse.csr_matrix(
            (X_train_npz['data'], X_train_npz['indices'], X_train_npz['indptr']),
            shape=X_train_npz['shape']
        )
        
        # Load labels
        y_train = np.load(os.path.join(preprocessed_data_dir, 'y_train_full.npy'))
        
        # Load original texts for duplicate detection
        # Try to find the CSV file in multiple locations
        csv_paths_to_try = [
            os.path.join(preprocessed_data_dir, train_file),
            os.path.join(input_dir, train_file),
            train_file  # Current directory
        ]
        
        train_df = None
        for csv_path in csv_paths_to_try:
            if os.path.exists(csv_path):
                train_df = pd.read_csv(csv_path)
                print(f"Found training CSV at: {csv_path}")
                break
        
        if train_df is None:
            raise FileNotFoundError(f"Could not find {train_file} in any of these locations: {csv_paths_to_try}")
        
        texts = train_df['medical_abstract'].values
        
        print(f"Loaded {X_train.shape[0]} training samples with {X_train.shape[1]} features")
        print(f"Sparse matrix density: {X_train.nnz / (X_train.shape[0] * X_train.shape[1]):.4f}")
        
        return X_train, y_train, texts
    
    def remove_duplicates(self, X_train, y_train, texts):
        """Remove conflicting duplicates from the training data."""
        # Detect duplicates (FIXED: use the correct method name)
        self.duplicate_remover.detect_duplicates(texts, y_train)
        
        # Get conflicting indices
        conflicting_indices = self.duplicate_remover.conflicting_indices
        
        if not conflicting_indices:
            print("No conflicting duplicates found.")
            # Return original data with all indices
            return X_train, y_train, np.arange(X_train.shape[0])
        
        # Create mask for non-conflicting samples
        all_indices = set(range(X_train.shape[0]))
        clean_indices = sorted(all_indices - conflicting_indices)
        clean_indices = np.array(clean_indices)
        
        # Filter the data
        X_clean = X_train[clean_indices]
        y_clean = y_train[clean_indices]
        
        print(f"Removed {len(conflicting_indices)} conflicting samples.")
        print(f"Clean dataset size: {len(clean_indices)}")
    
        return X_clean, y_clean, clean_indices
    
    def select_samples_kmedoids(self, X, y, original_indices, train_size, class_balance="equal", extra_frac=0.5):
        """Select samples using K-Medoids clustering"""
        print(f"Selecting {train_size} samples using K-Medoids...")
        
        unique_labels = np.unique(y)
        n_classes = len(unique_labels)
        
        if class_balance == "equal":
            base_samples_per_class = train_size // n_classes
            extra_samples = train_size % n_classes
            samples_per_class = [base_samples_per_class] * n_classes
            # Distribute extra samples to first few classes
            for i in range(extra_samples):
                samples_per_class[i] += 1
        else:
            # Proportional to class distribution
            class_counts = Counter(y)
            total_samples = len(y)
            samples_per_class = []
            for label in unique_labels:
                prop = class_counts[label] / total_samples
                samples_per_class.append(max(1, int(train_size * prop)))
        
        print(f"Target samples per class: {dict(zip(unique_labels, samples_per_class))}")
        
        selected_indices = []
        
        for i, label in enumerate(unique_labels):
            target_samples = samples_per_class[i]
            
            # Get indices for this class
            class_mask = (y == label)
            class_indices = np.where(class_mask)[0]
            X_class = X[class_indices]
            
            print(f"\nProcessing class {label}: {len(class_indices)} samples, target: {target_samples}")
            
            if len(class_indices) <= target_samples:
                # Take all samples if we have fewer than needed
                selected_class_indices = class_indices
                print(f"  Taking all {len(class_indices)} samples (fewer than target)")
            else:
                # Use K-Medoids clustering
                try:
                    # Determine number of clusters based on target samples and extra_frac
                    actual_clusters = min(target_samples, len(class_indices))
                    if extra_frac > 0:
                        actual_clusters = min(int(target_samples * (1 + extra_frac)), len(class_indices))
                    
                    print(f"  Running K-Medoids with {actual_clusters} clusters")
                    
                    start_time = time.time()
                    
                    # Convert sparse matrix to dense for K-Medoids
                    if scipy.sparse.issparse(X_class):
                        X_class_dense = X_class.toarray()
                    else:
                        X_class_dense = X_class
                    
                    # Run K-Medoids
                    kmedoids = KMedoids(
                        n_clusters=actual_clusters, 
                        init="k-medoids++", 
                        random_state=self.random_seed,
                        metric='euclidean'
                    )
                    kmedoids.fit(X_class_dense)
                    
                    # Get medoid indices within this class
                    medoid_indices_in_class = kmedoids.medoid_indices_
                    
                    # Map back to global indices
                    selected_class_indices = class_indices[medoid_indices_in_class]
                    
                    # If we got more than needed, randomly sample down
                    if len(selected_class_indices) > target_samples:
                        np.random.seed(self.random_seed + i)  # Different seed per class
                        selected_class_indices = np.random.choice(
                            selected_class_indices, 
                            target_samples, 
                            replace=False
                        )
                    
                    print(f"  K-Medoids completed in {time.time() - start_time:.2f} seconds")
                    print(f"  Selected {len(selected_class_indices)} samples")
                    
                except Exception as e:
                    print(f"  Error during K-Medoids for class {label}: {e}")
                    print(f"  Falling back to random selection")
                    
                    # Fallback to random selection
                    np.random.seed(self.random_seed + i)
                    selected_class_indices = np.random.choice(
                        class_indices, 
                        target_samples, 
                        replace=False
                    )
            
            selected_indices.extend(selected_class_indices)
        
        # Convert to numpy array and map back to original indices
        selected_indices = np.array(selected_indices)
        original_selected_indices = original_indices[selected_indices]
        
        print(f"\nTotal selected samples: {len(original_selected_indices)}")
        
        # Verify class distribution
        selected_labels = y[selected_indices]
        final_class_counts = Counter(selected_labels)
        print("Final class distribution:")
        for label in unique_labels:
            print(f"  Class {label}: {final_class_counts[label]} samples")
        
        return original_selected_indices

# Add this as a new cell in your Jupyter notebook
# K-MEDOIDS SAMPLE SELECTION WITH DUPLICATE REMOVAL (Notebook Version)

import json

# Configuration - replace argparse with direct variable assignments
class Args:
    def __init__(self):
        self.preprocessed_data_dir = 'preprocessed_data/'  # Change this to your data directory
        self.output_file = 'selected_train_ids_kmedoids.json'
        self.train_size = 1000  # Change this to your desired size
        self.random_seed = 42
        self.class_balance = 'equal'  # or 'proportional'
        self.extra_frac = 0.5

args = Args()

print("="*80)
print("K-MEDOIDS SAMPLE SELECTION WITH DUPLICATE REMOVAL")
print("="*80)
print(f"Preprocessed data directory: {args.preprocessed_data_dir}")
print(f"Output file: {args.output_file}")
print(f"Target train size: {args.train_size}")
print(f"Random seed: {args.random_seed}")
print(f"Class balance: {args.class_balance}")
print(f"Extra fraction: {args.extra_frac}")
print("="*80)

# Initialize selector
selector = KMedoidsSelector(random_seed=args.random_seed)

# Load data
X_train, y_train, texts = selector.load_data(args.preprocessed_data_dir)

# Remove conflicting duplicates
X_clean, y_clean, clean_indices = selector.remove_duplicates(X_train, y_train, texts)

# Select samples using K-Medoids
selected_indices = selector.select_samples_kmedoids(
    X_clean, y_clean, clean_indices, 
    args.train_size, args.class_balance, args.extra_frac
)

# Save results
result = {
    'selected_ids': selected_indices.tolist(),
    'total_selected': len(selected_indices),
    'method': 'kmedoids_with_duplicate_removal',
    'parameters': {
        'train_size': args.train_size,
        'random_seed': args.random_seed,
        'class_balance': args.class_balance,
        'extra_frac': args.extra_frac
    },
    'duplicate_info': {
        'conflicting_groups': len(selector.duplicate_remover.duplicate_groups) if selector.duplicate_remover.duplicate_groups else 0,
        'conflicting_samples_removed': len(selector.duplicate_remover.conflicting_indices)
    }
}

with open(args.output_file, 'w') as f:
    json.dump(result, f, indent=2)

print(f"\nResults saved to {args.output_file}")
print("="*80)


import json
import numpy as np
import subprocess
import os
import time
from typing import List

# Configuration
PREPROCESSED_DATA_DIR = "preprocessed_data/"
RANDOM_SEED = 42
N_FOLDS = 3  # Not used in K-Medoids but kept for consistency
CLASS_BALANCE = "equal"
EXTRA_FRAC = 0.5

# Define training sizes to test
FIXED_SMALL_SIZES = [25, 60, 100, 200, 500, 1000]

def run_command(command: str, description: str = ""):
    """Run a shell command and handle errors"""
    print(f"\n{'='*60}")
    if description:
        print(f"Running: {description}")
    print(f"Command: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def load_and_display_results(results_file: str, log_file_handle, train_size: int):
    """Load evaluation results and write to log"""
    try:
        with open(results_file, 'r') as rf:
            metrics = json.load(rf)
        
        # Write results to log file
        log_file_handle.write("--- Evaluation Results ---\n")
        
        # Handle different possible metric keys (for compatibility)
        f1_scores = metrics.get('f1_macro_scores', metrics.get('individual_f1_scores', []))
        f1_mean = metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0))
        f1_std = metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0))
        
        log_file_handle.write(f"Individual F1 Macro Scores: {f1_scores}\n")
        log_file_handle.write(f"Average F1 Macro Score: {f1_mean:.4f}\n")
        log_file_handle.write(f"Standard Deviation of F1 Macro Scores: {f1_std:.4f}\n")
        
        # Also display to console
        print(f"\n{'='*40}")
        print(f"RESULTS FOR TRAIN SIZE {train_size}")
        print('='*40)
        print(f"F1 Macro Scores: {f1_scores}")
        print(f"Average F1 Macro: {f1_mean:.4f}")
        print(f"Standard Deviation: {f1_std:.4f}")
        print('='*40)
        
        return {
            'train_size': train_size,
            'f1_scores': f1_scores,
            'f1_mean': f1_mean,
            'f1_std': f1_std,
            'metrics': metrics
        }
        
    except Exception as e:
        print(f"Error loading results: {e}")
        log_file_handle.write(f"Error loading results: {e}\n")
        return None

def main():
    print("="*80)
    print("K-MEDOIDS SAMPLE SELECTION SWEEP")
    print("="*80)
    print(f"Training sizes to test: {FIXED_SMALL_SIZES}")
    print(f"Preprocessed data directory: {PREPROCESSED_DATA_DIR}")
    print(f"Random seed: {RANDOM_SEED}")
    print(f"Class balance: {CLASS_BALANCE}")
    print(f"Extra fraction: {EXTRA_FRAC}")
    print("="*80)
    
    # Check if required files exist
    required_files = [
        "select_kmedoids_medical.py",
        "eval_medical.py"
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"ERROR: Required file {file} not found!")
            return
    
    if not os.path.exists(PREPROCESSED_DATA_DIR):
        print(f"ERROR: Preprocessed data directory {PREPROCESSED_DATA_DIR} not found!")
        return
    
    # Initialize results
    log_file = 'kmedoids_sweep_results.txt'
    all_results = []
    
    start_time = time.time()
    
    with open(log_file, 'w') as f:
        f.write("="*80 + "\n")
        f.write("K-MEDOIDS SAMPLE SELECTION SWEEP RESULTS\n")
        f.write("="*80 + "\n")
        f.write(f"Configuration:\n")
        f.write(f"  Training sizes: {FIXED_SMALL_SIZES}\n")
        f.write(f"  Random seed: {RANDOM_SEED}\n")
        f.write(f"  Class balance: {CLASS_BALANCE}\n")
        f.write(f"  Extra fraction: {EXTRA_FRAC}\n")
        f.write("="*80 + "\n\n")
        
        for train_size in FIXED_SMALL_SIZES:
            f.write("="*80 + "\n")
            f.write(f"TRAINING SIZE: {train_size}\n")
            f.write("="*80 + "\n")
            
            print(f"\n{'='*60}")
            print(f"PROCESSING TRAIN SIZE: {train_size}")
            print('='*60)
            
            # Step 1: Run K-Medoids selection
            selection_command = f"""python select_kmedoids_medical.py \
                --preprocessed_data_dir="{PREPROCESSED_DATA_DIR}" \
                --output_file="selected_train_ids_kmedoids.json" \
                --train_size={train_size} \
                --random_seed={RANDOM_SEED} \
                --class_balance="{CLASS_BALANCE}" \
                --extra_frac={EXTRA_FRAC}"""
            
            if not run_command(selection_command, f"K-Medoids selection for {train_size} samples"):
                f.write(f"ERROR: Failed to run K-Medoids selection for train_size={train_size}\n")
                continue
            
            # Step 2: Load and verify selected samples
            try:
                with open('selected_train_ids_kmedoids.json', 'r') as rf:
                    selected_data = json.load(rf)
                
                selected_ids = np.array(selected_data['selected_ids'])
                
                # Get label counts
                y_train_full = np.load(os.path.join(PREPROCESSED_DATA_DIR, 'y_train_full.npy'))
                labels = y_train_full[selected_ids]
                unique, counts = np.unique(labels, return_counts=True)
                
                # Write class distribution
                f.write(f"\nSelected training set created with {train_size} samples.\n")
                f.write(f"Actual selected samples: {len(selected_ids)}\n")
                
                duplicate_info = selected_data.get('duplicate_info', {})
                if duplicate_info:
                    f.write(f"Conflicting duplicate groups removed: {duplicate_info.get('conflicting_groups', 0)}\n")
                    f.write(f"Conflicting samples removed: {duplicate_info.get('conflicting_samples_removed', 0)}\n")
                
                f.write("Class distribution:\n")
                for label, count in zip(unique, counts):
                    f.write(f"  Label {label}: {count} samples\n")
                
                print(f"Selected {len(selected_ids)} samples with distribution:")
                for label, count in zip(unique, counts):
                    print(f"  Label {label}: {count} samples")
                
            except Exception as e:
                print(f"Error loading selected samples: {e}")
                f.write(f"Error loading selected samples: {e}\n")
                continue
            
            # Step 3: Run evaluation
            eval_command = f"""python eval_medical.py \
                --preprocessed_data_dir {PREPROCESSED_DATA_DIR} \
                --selected_ids_json selected_train_ids_kmedoids.json \
                --metrics_output_file results_selected_subset.json"""
            
            if not run_command(eval_command, f"Evaluation for {train_size} samples"):
                f.write(f"ERROR: Failed to run evaluation for train_size={train_size}\n")
                continue
            
            # Step 4: Load and store results
            result = load_and_display_results('results_selected_subset.json', f, train_size)
            if result:
                all_results.append(result)
            
            # Add spacing for readability
            f.write("\n\n")
            f.flush()  # Force write to disk
        
        # Write summary
        f.write("="*80 + "\n")
        f.write("SUMMARY OF ALL RESULTS\n")
        f.write("="*80 + "\n")
        
        if all_results:
            f.write(f"{'Train Size':<12} {'F1 Mean':<10} {'F1 Std':<10} {'Best F1':<10}\n")
            f.write("-" * 50 + "\n")
            
            for result in all_results:
                best_f1 = max(result['f1_scores']) if result['f1_scores'] else 0.0
                f.write(f"{result['train_size']:<12} {result['f1_mean']:<10.4f} {result['f1_std']:<10.4f} {best_f1:<10.4f}\n")
            
            # Find best overall result
            best_result = max(all_results, key=lambda x: x['f1_mean'])
            f.write(f"\nBest average F1 score: {best_result['f1_mean']:.4f} at train_size={best_result['train_size']}\n")
            
        else:
            f.write("No successful results to summarize.\n")
    
    end_time = time.time()
    
    print(f"\n{'='*80}")
    print("SWEEP COMPLETED!")
    print(f"Total time: {end_time - start_time:.2f} seconds")
    print(f"Results saved to: {log_file}")
    print(f"Processed {len(all_results)}/{len(FIXED_SMALL_SIZES)} train sizes successfully")
    
    if all_results:
        print("\nSUMMARY:")
        print(f"{'Train Size':<12} {'F1 Mean':<10} {'F1 Std':<10}")
        print("-" * 35)
        for result in all_results:
            print(f"{result['train_size']:<12} {result['f1_mean']:<10.4f} {result['f1_std']:<10.4f}")
        
        best_result = max(all_results, key=lambda x: x['f1_mean'])
        print(f"\nBest: F1={best_result['f1_mean']:.4f} at size={best_result['train_size']}")
    
    print("="*80)

if __name__ == "__main__":
    main()


# K-MEDOIDS SAMPLE SELECTION SWEEP (Notebook Version)
import json
import numpy as np

# Configuration
PREPROCESSED_DATA_DIR = "preprocessed_data/"
RANDOM_SEED = 42
CLASS_BALANCE = "equal"
EXTRA_FRAC = 0.5

# Define training sizes to test
FIXED_SMALL_SIZES = [25, 60, 100, 200, 500, 1000]

print("="*80)
print("K-MEDOIDS SAMPLE SELECTION SWEEP")
print("="*80)
print(f"Training sizes to test: {FIXED_SMALL_SIZES}")
print(f"Preprocessed data directory: {PREPROCESSED_DATA_DIR}")
print(f"Random seed: {RANDOM_SEED}")
print(f"Class balance: {CLASS_BALANCE}")
print(f"Extra fraction: {EXTRA_FRAC}")
print("="*80)

log_file = 'kmedoids_sweep_results.txt'
results = []

with open(log_file, 'w') as f:
    f.write("="*80 + "\n")
    f.write("K-MEDOIDS SAMPLE SELECTION SWEEP RESULTS\n")
    f.write("="*80 + "\n")
    f.write(f"Configuration:\n")
    f.write(f"  Training sizes: {FIXED_SMALL_SIZES}\n")
    f.write(f"  Random seed: {RANDOM_SEED}\n")
    f.write(f"  Class balance: {CLASS_BALANCE}\n")
    f.write(f"  Extra fraction: {EXTRA_FRAC}\n")
    f.write("="*80 + "\n\n")
    
    for train_size in FIXED_SMALL_SIZES:
        print(f"\n{'='*60}")
        print(f"PROCESSING TRAIN SIZE: {train_size}")
        print('='*60)
        
        f.write("="*80 + "\n")
        f.write(f"TRAINING SIZE: {train_size}\n")
        f.write("="*80 + "\n")
        
        # Run K-Medoids selection
        !python select_kmedoids_medical.py \
            --preprocessed_data_dir="{PREPROCESSED_DATA_DIR}" \
            --output_file="selected_train_ids_kmedoids.json" \
            --train_size={train_size} \
            --random_seed={RANDOM_SEED} \
            --class_balance="{CLASS_BALANCE}" \
            --extra_frac={EXTRA_FRAC}
        
        # Load selected samples
        with open('selected_train_ids_kmedoids.json', 'r') as rf:
            selected_data = json.load(rf)
            selected_ids = np.array(selected_data['selected_ids'])
        
        # Get label counts
        labels = np.load(f'{PREPROCESSED_DATA_DIR}/y_train_full.npy')[selected_ids]
        unique, counts = np.unique(labels, return_counts=True)
        
        # Write class distribution
        f.write(f"\\nSelected training set created with {train_size} samples.\\n")
        f.write(f"Actual selected samples: {len(selected_ids)}\\n")
        
        # Handle duplicate info if available
        duplicate_info = selected_data.get('duplicate_info', {})
        if duplicate_info:
            f.write(f"Conflicting duplicate groups removed: {duplicate_info.get('conflicting_groups', 0)}\\n")
            f.write(f"Conflicting samples removed: {duplicate_info.get('conflicting_samples_removed', 0)}\\n")
        
        f.write("Class distribution:\\n")
        for label, count in zip(unique, counts):
            f.write(f"  Label {label}: {count} samples\\n")
        
        print(f"Selected {len(selected_ids)} samples with distribution:")
        for label, count in zip(unique, counts):
            print(f"  Label {label}: {count} samples")
        
        # Run evaluation
        !python eval_medical.py \
            --preprocessed_data_dir {PREPROCESSED_DATA_DIR} \
            --selected_ids_json selected_train_ids_kmedoids.json \
            --metrics_output_file results_selected_subset.json
        
        # Store and log evaluation results
        with open('results_selected_subset.json', 'r') as rf:
            metrics = json.load(rf)
            f.write("--- Evaluation Results ---\\n")
            
            # Handle different possible metric keys (for compatibility)
            f1_scores = metrics.get('f1_macro_scores', metrics.get('individual_f1_scores', []))
            f1_mean = metrics.get('average_f1_macro', metrics.get('f1_macro_mean', 0.0))
            f1_std = metrics.get('std_f1_macro', metrics.get('f1_macro_std', 0.0))
            
            f.write(f"Individual F1 Macro Scores: {f1_scores}\\n")
            f.write(f"Average F1 Macro Score: {f1_mean:.4f}\\n")
            f.write(f"Standard Deviation of F1 Macro Scores: {f1_std:.4f}\\n")
            
            # Store for summary
            results.append({
                'train_size': train_size,
                'f1_scores': f1_scores,
                'f1_mean': f1_mean,
                'f1_std': f1_std
            })
            
            # Also display to console
            print(f"\\n{'='*40}")
            print(f"RESULTS FOR TRAIN SIZE {train_size}")
            print('='*40)
            print(f"F1 Macro Scores: {f1_scores}")
            print(f"Average F1 Macro: {f1_mean:.4f}")
            print(f"Standard Deviation: {f1_std:.4f}")
            print('='*40)
        
        # Add spacing for readability
        f.write("\\n\\n")
        f.flush()  # Force write to disk
    
    # Write summary
    f.write("="*80 + "\\n")
    f.write("SUMMARY OF ALL RESULTS\\n")
    f.write("="*80 + "\\n")
    
    if results:
        f.write(f"{'Train Size':<12} {'F1 Mean':<10} {'F1 Std':<10} {'Best F1':<10}\\n")
        f.write("-" * 50 + "\\n")
        
        for result in results:
            best_f1 = max(result['f1_scores']) if result['f1_scores'] else 0.0
            f.write(f"{result['train_size']:<12} {result['f1_mean']:<10.4f} {result['f1_std']:<10.4f} {best_f1:<10.4f}\\n")
        
        # Find best overall result
        best_result = max(results, key=lambda x: x['f1_mean'])
        f.write(f"\\nBest average F1 score: {best_result['f1_mean']:.4f} at train_size={best_result['train_size']}\\n")

print(f'\\nSweep complete! Detailed log saved to {log_file}')

# Display final summary
if results:
    print("\\nFINAL SUMMARY:")
    print(f"{'Train Size':<12} {'F1 Mean':<10} {'F1 Std':<10}")
    print("-" * 35)
    for result in results:
        print(f"{result['train_size']:<12} {result['f1_mean']:<10.4f} {result['f1_std']:<10.4f}")
    
    best_result = max(results, key=lambda x: x['f1_mean'])
    print(f"\\nBest: F1={best_result['f1_mean']:.4f} at size={best_result['train_size']}")

print("="*80)

